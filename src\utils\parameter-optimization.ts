/**
 * 参数优化工具
 * 提供网格搜索、遗传算法、贝叶斯优化等参数优化方法
 */

export interface ParameterRange {
  name: string;
  min: number;
  max: number;
  step?: number;
  type: 'int' | 'float';
}

export interface OptimizationResult {
  parameters: Record<string, number>;
  score: number;
  metrics: any;
}

export interface OptimizationConfig { 
  method: 'grid' | 'genetic' | 'bayesian';
  maxIterations: number;
  targetMetric: string; // sharpeRatio, totalReturn', 'calmarRatio' etc.
  maximize: boolean;
}

/**
 * 网格搜索优化
 */
export class GridSearchOptimizer {
  private parameterRanges: ParameterRange[];
  private evaluateFunction: (params: Record<string, number>) => Promise<any>;

  constructor(
    parameterRanges: ParameterRange[],
    evaluateFunction: (params: Record<string, number>) => Promise<any>
  ) {
    this.parameterRanges = parameterRanges;
    this.evaluateFunction = evaluateFunction;
  }

  async optimize(config: OptimizationConfig): Promise<OptimizationResult[]> {
    const results: OptimizationResult[] = [];
    const parameterCombinations = this.generateParameterCombinations();

    let iteration = 0;
    for (const params of parameterCombinations) {
      if (iteration >= config.maxIterations) break;

      try {
        const metrics = await this.evaluateFunction(params);
        const score = this.extractScore(metrics, config.targetMetric);
        
        results.push({
          parameters: params,
          score,
          metrics
        });

        iteration++;
      } catch (error) {'
        console.warn('参数组合评估失败:', params, error);
      }
    }

    // 根据目标排序
    results.sort((a, b) => config.maximize ? b.score - a.score : a.score - b.score);
    return results;
  }

  private generateParameterCombinations(): Record<string, number>[] {
    const combinations: Record<string, number>[] = [];
    
    const generateRecursive = (index: number, current: Record<string, number>) => {
      if (index >= this.parameterRanges.length) {
        combinations.push({ ...current });
        return;
      }

      const range = this.parameterRanges[index];
      const step = range.step || (range.type === int ? 1 : (range.max - range.min) / 10);
      
      for (let value = range.min; value <= range.max; value += step) {'
        const actualValue = range.type === 'int' ? Math.round(value) : value;
        current[range.name] = actualValue;
        generateRecursive(index + 1, current);
      }
    };

    generateRecursive(0, {});
    return combinations;
  }

  private extractScore(metrics: any, targetMetric: string): number {
    return metrics[targetMetric] || 0;
  }
}

/**
 * 遗传算法优化
 */
export class GeneticAlgorithmOptimizer {
  private parameterRanges: ParameterRange[];
  private evaluateFunction: (params: Record<string, number>) => Promise<any>;
  private populationSize: number;
  private mutationRate: number;
  private crossoverRate: number;

  constructor(
    parameterRanges: ParameterRange[],
    evaluateFunction: (params: Record<string, number>) => Promise<any>,
    options: {
      populationSize?: number;
      mutationRate?: number;
      crossoverRate?: number;
    } = {}
  ) {
    this.parameterRanges = parameterRanges;
    this.evaluateFunction = evaluateFunction;
    this.populationSize = options.populationSize || 50;
    this.mutationRate = options.mutationRate || 0.1;
    this.crossoverRate = options.crossoverRate || 0.8;
  }

  async optimize(config: OptimizationConfig): Promise<OptimizationResult[]> {
    let population = this.initializePopulation();
    const results: OptimizationResult[] = [];

    for (let generation = 0; generation < config.maxIterations; generation++) {
      // 评估当前种群
      const evaluatedPopulation = await this.evaluatePopulation(population, config.targetMetric);
      
      // 记录最佳结果
      const best = evaluatedPopulation[0];
      results.push(best);

      // 选择、交叉、变异
      population = this.evolvePopulation(evaluatedPopulation, config.maximize);
    }

    return results.sort((a, b) => config.maximize ? b.score - a.score : a.score - b.score);
  }

  private initializePopulation(): Record<string, number>[] {
    const population: Record<string, number>[] = [];
    
    for (let i = 0; i < this.populationSize; i++) {
      const individual: Record<string, number> = {};
      
      for (const range of this.parameterRanges) {
        const randomValue = Math.random() * (range.max - range.min) + range.min;
        individual[range.name] = range.type === int ? Math.round(randomValue) : randomValue;
      }
      
      population.push(individual);
    }
    
    return population;
  }

  private async evaluatePopulation(
    population: Record<string, number>[],
    targetMetric: string
  ): Promise<OptimizationResult[]> {
    const results: OptimizationResult[] = [];
    
    for (const individual of population) {
      try {
        const metrics = await this.evaluateFunction(individual);
        const score = metrics[targetMetric] || 0;
        
        results.push({
          parameters: individual,
          score,
          metrics
        });
      } catch (error) {
        // 如果评估失败，给予很低的分数
        results.push({
          parameters: individual,
          score: -Infinity,
          metrics: {}
        });
      }
    }
    
    return results.sort((a, b) => b.score - a.score);
  }

  private evolvePopulation(
    evaluatedPopulation: OptimizationResult[],
    _maximize: boolean
  ): Record<string, number>[] {
    const newPopulation: Record<string, number>[] = [];
    
    // 保留最优个体（精英策略）
    const eliteCount = Math.floor(this.populationSize * 0.1);
    for (let i = 0; i < eliteCount; i++) {
      newPopulation.push({ ...evaluatedPopulation[i].parameters });
    }
    
    // 生成新个体
    while (newPopulation.length < this.populationSize) {
      const parent1 = this.selectParent(evaluatedPopulation);
      const parent2 = this.selectParent(evaluatedPopulation);
      
      let offspring = this.crossover(parent1.parameters, parent2.parameters);
      offspring = this.mutate(offspring);
      
      newPopulation.push(offspring);
    }
    
    return newPopulation;
  }

  private selectParent(population: OptimizationResult[]): OptimizationResult {
    // 锦标赛选择
    const tournamentSize = 3;
    const tournament = [];
    
    for (let i = 0; i < tournamentSize; i++) {
      const randomIndex = Math.floor(Math.random() * population.length);
      tournament.push(population[randomIndex]);
    }
    
    return tournament.sort((a, b) => b.score - a.score)[0];
  }

  private crossover(
    parent1: Record<string, number>,
    parent2: Record<string, number>
  ): Record<string, number> {
    const offspring: Record<string, number> = {};
    
    for (const range of this.parameterRanges) {
      if (Math.random() < this.crossoverRate) {
        // 算术交叉
        const alpha = Math.random();
        const value = alpha * parent1[range.name] + (1 - alpha) * parent2[range.name];
        offspring[range.name] = range.type === int ? Math.round(value) : value;
      } else {
        offspring[range.name] = Math.random() < 0.5 ? parent1[range.name] : parent2[range.name];
      }
    }
    
    return offspring;
  }

  private mutate(individual: Record<string, number>): Record<string, number> {
    const mutated = { ...individual };
    
    for (const range of this.parameterRanges) {
      if (Math.random() < this.mutationRate) {
        // 高斯变异
        const sigma = (range.max - range.min) * 0.1;
        const mutation = this.gaussianRandom() * sigma;
        let newValue = mutated[range.name] + mutation;
        
        // 边界处理
        newValue = Math.max(range.min, Math.min(range.max, newValue));
        mutated[range.name] = range.type === int ? Math.round(newValue) : newValue;
      }
    }
    
    return mutated;
  }

  private gaussianRandom(): number {
    // Box-Muller变换生成高斯随机数
    let u = 0, v = 0;
    while (u === 0) u = Math.random();
    while (v === 0) v = Math.random();
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  }
}

/**
 * 贝叶斯优化（简化版）
 */
export class BayesianOptimizer {
  private parameterRanges: ParameterRange[];
  private evaluateFunction: (params: Record<string, number>) => Promise<any>;
  private observations: { params: Record<string, number>; score: number }[] = [];

  constructor(
    parameterRanges: ParameterRange[],
    evaluateFunction: (params: Record<string, number>) => Promise<any>
  ) {
    this.parameterRanges = parameterRanges;
    this.evaluateFunction = evaluateFunction;
  }

  async optimize(config: OptimizationConfig): Promise<OptimizationResult[]> {
    const results: OptimizationResult[] = [];

    // 初始随机采样
    const initialSamples = Math.min(10, config.maxIterations);
    for (let i = 0; i < initialSamples; i++) {
      const params = this.randomSample();
      const metrics = await this.evaluateFunction(params);
      const score = metrics[config.targetMetric] || 0;
      
      this.observations.push({ params, score });
      results.push({ parameters: params, score, metrics });
    }

    // 贝叶斯优化迭代
    for (let i = initialSamples; i < config.maxIterations; i++) {
      const nextParams = this.acquireNext();
      const metrics = await this.evaluateFunction(nextParams);
      const score = metrics[config.targetMetric] || 0;
      
      this.observations.push({ params: nextParams, score });
      results.push({ parameters: nextParams, score, metrics });
    }

    return results.sort((a, b) => config.maximize ? b.score - a.score : a.score - b.score);
  }

  private randomSample(): Record<string, number> {
    const params: Record<string, number> = {};
    
    for (const range of this.parameterRanges) {
      const randomValue = Math.random() * (range.max - range.min) + range.min;
      params[range.name] = range.type === int ? Math.round(randomValue) : randomValue;
    }
    
    return params;
  }

  private acquireNext(): Record<string, number> {
    // 简化的获取函数：在最佳点附近采样
    if (this.observations.length === 0) {
      return this.randomSample();
    }

    const bestObservation = this.observations.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    const params: Record<string, number> = {};
    
    for (const range of this.parameterRanges) {
      const bestValue = bestObservation.params[range.name];
      const sigma = (range.max - range.min) * 0.2;
      const newValue = bestValue + this.gaussianRandom() * sigma;
      
      const clampedValue = Math.max(range.min, Math.min(range.max, newValue));
      params[range.name] = range.type === int ? Math.round(clampedValue) : clampedValue;
    }
    
    return params;
  }

  private gaussianRandom(): number {
    let u = 0, v = 0;
    while (u === 0) u = Math.random();
    while (v === 0) v = Math.random();
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  }
}