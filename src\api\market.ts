/**
 * 市场数据API
 */
import { httpClient } from '../utils/http'
import { API_PATHS, ENV_CONFIG } from '../utils/constants'
import type { ApiResponse, ListResponse } from '../utils/http'
// 行情数据类型
export interface QuoteData {
  symbol: string
  name: string
  currentPrice: number
  previousClose?: number
  change: number
  changePercent: number
  high?: number
  low?: number
  openPrice?: number
  volume: number
  turnover?: number
  timestamp?: number
  status?: trading | 'paused' | 'closed'
  industry?: string
}

// K线数据类型
export interface KLineData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
  turnover: number
  symbol?: string
}

// 市场深度数据
export interface OrderBookItem {
  price: number
  quantity: number
  amount: number
}

export interface OrderBookData {
  symbol: string
  timestamp: number
  bids: OrderBookItem[]
  asks: OrderBookItem[]
}

// 股票信息
export interface StockInfo {
  symbol: string
  name: string
  exchange: string
  market: string
  sector: string
  industry: string
  listDate: string
  totalShares: number
  floatShares: number
  marketCap: number
  pe: number
  pb: number
  eps: number
  roe: number
  status: string
  currentPrice?: number
  change?: number
  changePercent?: number
  volume?: number
  amount?: number
  high?: number
  low?: number
  open?: number
  close?: number
}

// 市场概览
export interface MarketOverview {
  indices: Array<{
    name: string
    value: number
    change: number
    changePercent: number
  }>
  stats: {
    totalStocks: number
    advancers: number
    decliners: number
    unchanged: number
    totalVolume: number
    totalTurnover: number
  }
  timestamp: number
}

// 板块数据
export interface SectorData {
  name: string
  code: string
  change: number
  changePercent: number
  volume: number
  turnover: number
  stocks: number
  leadingStock: {
    symbol: string
    name: string
    changePercent: number
  }
}

// 新闻数据
export interface NewsItem {
  id: string
  title: string
  summary: string
  content?: string
  source: string
  publishTime: string
  url?: string
  tags: string[]
  relatedSymbols?: string[]
}

// 请求参数类型
export interface QuoteParams {
  symbols: string[]
  fields?: string[]
}

export interface KLineParams {
  symbol: string
  period: string
  limit?: number
  startTime?: string
  endTime?: string
}

export interface SearchParams {
  keyword: string
  limit?: number
  type?: string
}

// 模拟数据生成函数
const generateMockQuote = (symbol: string): QuoteData => {
  const basePrice = Math.random() * 100 + 10
  const change = (Math.random() - 0.5) * 10
  const changePercent = (change / basePrice) * 100

  return {
    symbol,
    name: `股票${symbol}`,
    currentPrice: Number((basePrice + change).toFixed(2)),
    previousClose: Number(basePrice.toFixed(2)),
    change: Number(change.toFixed(2)),
    changePercent: Number(changePercent.toFixed(2)),
    high: Number((basePrice + Math.abs(change) + Math.random() * 5).toFixed(2)),
    low: Number((basePrice - Math.abs(change) - Math.random() * 5).toFixed(2)),
    openPrice: Number((basePrice + (Math.random() - 0.5) * 2).toFixed(2)),
    volume: Math.floor(Math.random() * 1000000),
    turnover: Math.floor(Math.random() * 100000000),
    timestamp: Date.now(),
    status: trading,
    industry: '科技'
  }
}

const generateMockKLine = (symbol: string, count: number = 100): KLineData[] => {
  const data: KLineData[] = []
  let basePrice = Math.random() * 100 + 50
  const now = Date.now()
  
  for (let i = count - 1; i >= 0; i--) {
    const timestamp = now - i * 24 * 60 * 60 * 1000 // 每天一个数据点
    const change = (Math.random() - 0.5) * 10
    const open = basePrice
    const close = basePrice + change
    const high = Math.max(open, close) + Math.random() * 5
    const low = Math.min(open, close) - Math.random() * 5
    
    data.push({
      timestamp,
      open: Number(open.toFixed(2)),
      high: Number(high.toFixed(2)),
      low: Number(low.toFixed(2)),
      close: Number(close.toFixed(2)),
      volume: Math.floor(Math.random() * 1000000),
      turnover: Math.floor(Math.random() * 100000000),
      symbol
    })
    
    basePrice = close
  }
  
  return data
}

/**
 * 市场数据API类
 */
export class MarketAPI {
  private useMock = ENV_CONFIG.enableMock

  /**
   * 获取实时行情
   */
  async getQuote(symbols: string | string[]): Promise<QuoteData[]> {
    const symbolList = Array.isArray(symbols) ? symbols : [symbols]

    if (this.useMock) {
      return symbolList.map(symbol => generateMockQuote(symbol))
    }

    try {
      const response = await httpClient.get<QuoteData[]>(API_PATHS.MARKET.QUOTE, {
        symbols: symbolList.join(,)
      })
      return response.data || []
    } catch (error) {
      console.warn(获取行情API调用失败，使用模拟数据:', error)
      return symbolList.map(symbol => generateMockQuote(symbol))
    }
  }

  /**
   * 获取K线数据
   */
  async getKLineData(params: KLineParams): Promise<KLineData[]> {
    if (this.useMock) {
      return generateMockKLine(params.symbol, params.limit || 100)
    }

    try {
      const response = await httpClient.get<KLineData[]>(API_PATHS.MARKET.KLINE, {
        symbol: params.symbol,
        period: params.period,
        limit: params.limit || 1000,
        start_time: params.startTime,
        end_time: params.endTime
      })
      return response.data || []
    } catch (error) {'
      console.warn('获取K线数据API调用失败，使用模拟数据:', error)
      return generateMockKLine(params.symbol, params.limit || 100)
    }
  }

  /**
   * 搜索股票
   */
  async search(params: SearchParams): Promise<StockInfo[]> {
    if (this.useMock) {
      const mockResults: StockInfo[] = [
        { 
          symbol: '000001',
          name: '平安银行',
          exchange: 'SZ',
          market: '深圳',
          sector: '金融',
          industry: '银行',
          listDate: '1991-04-03',
          totalShares: 19405918198,
          floatShares: 19405918198,
          marketCap: 280000000000,
          pe: 5.2,
          pb: 0.8,
          eps: 2.1,
          roe: 12.5,'
          status: 'normal'
        }
      ].filter(stock => 
        stock.name.includes(params.keyword) || 
        stock.symbol.includes(params.keyword)
      )
      
      return mockResults.slice(0, params.limit || 10)
    }

    try {
      const response = await httpClient.get<StockInfo[]>(API_PATHS.MARKET.SEARCH, {
        keyword: params.keyword,
        limit: params.limit || 10,
        type: params.type
      })
      return response.data || []
    } catch (error) {'
      console.warn('搜索API调用失败，使用模拟数据:', error)
      return []
    }
  }

  /**
   * 获取市场概览
   */
  async getOverview(): Promise<MarketOverview> {
    if (this.useMock) {
      return {
        indices: ['
          { name: '上证指数', value: 3200.5, change: 15.2, changePercent: 0.48 },'
          { name: '深证成指', value: 12500.8, change: -8.5, changePercent: -0.07 },'
          { name: '创业板指', value: 2800.3, change: 25.6, changePercent: 0.92 }
        ],
        stats: {
          totalStocks: 4500,
          advancers: 2100,
          decliners: 1800,
          unchanged: 600,
          totalVolume: 450000000,
          totalTurnover: 520000000000
        },
        timestamp: Date.now()
      }
    }

    try {
      const response = await httpClient.get<MarketOverview>(API_PATHS.MARKET.OVERVIEW)
      return response.data!
    } catch (error) {'
      console.warn('获取市场概览API调用失败，使用模拟数据:', error)
      return {
        indices: [],
        stats: {
          totalStocks: 0,
          advancers: 0,
          decliners: 0,
          unchanged: 0,
          totalVolume: 0,
          totalTurnover: 0
        },
        timestamp: Date.now()
      }
    }
  }
}

// 创建实例
export const marketApi = new MarketAPI()

// 导出默认实例
export default marketApi