export declare const baseTheme: string & {
    className: string;
    selector: string;
} & {
    colors: {
        loContrast: import("@stitches/core/types/theme").Token<"loContrast", string, "colors", "hope">;
        background: import("@stitches/core/types/theme").Token<"background", string, "colors", "hope">;
        focusRing: import("@stitches/core/types/theme").Token<"focusRing", string, "colors", "hope">;
        closeButtonHoverBackground: import("@stitches/core/types/theme").Token<"closeButtonHoverBackground", string, "colors", "hope">;
        closeButtonActiveBackground: import("@stitches/core/types/theme").Token<"closeButtonActiveBackground", string, "colors", "hope">;
        progressStripe: import("@stitches/core/types/theme").Token<"progressStripe", string, "colors", "hope">;
        danger1: import("@stitches/core/types/theme").Token<"danger1", string, "colors", "hope">;
        danger2: import("@stitches/core/types/theme").Token<"danger2", string, "colors", "hope">;
        danger3: import("@stitches/core/types/theme").Token<"danger3", string, "colors", "hope">;
        danger4: import("@stitches/core/types/theme").Token<"danger4", string, "colors", "hope">;
        danger5: import("@stitches/core/types/theme").Token<"danger5", string, "colors", "hope">;
        danger6: import("@stitches/core/types/theme").Token<"danger6", string, "colors", "hope">;
        danger7: import("@stitches/core/types/theme").Token<"danger7", string, "colors", "hope">;
        danger8: import("@stitches/core/types/theme").Token<"danger8", string, "colors", "hope">;
        danger9: import("@stitches/core/types/theme").Token<"danger9", string, "colors", "hope">;
        danger10: import("@stitches/core/types/theme").Token<"danger10", string, "colors", "hope">;
        danger11: import("@stitches/core/types/theme").Token<"danger11", string, "colors", "hope">;
        danger12: import("@stitches/core/types/theme").Token<"danger12", string, "colors", "hope">;
        warning1: import("@stitches/core/types/theme").Token<"warning1", string, "colors", "hope">;
        warning2: import("@stitches/core/types/theme").Token<"warning2", string, "colors", "hope">;
        warning3: import("@stitches/core/types/theme").Token<"warning3", string, "colors", "hope">;
        warning4: import("@stitches/core/types/theme").Token<"warning4", string, "colors", "hope">;
        warning5: import("@stitches/core/types/theme").Token<"warning5", string, "colors", "hope">;
        warning6: import("@stitches/core/types/theme").Token<"warning6", string, "colors", "hope">;
        warning7: import("@stitches/core/types/theme").Token<"warning7", string, "colors", "hope">;
        warning8: import("@stitches/core/types/theme").Token<"warning8", string, "colors", "hope">;
        warning9: import("@stitches/core/types/theme").Token<"warning9", string, "colors", "hope">;
        warning10: import("@stitches/core/types/theme").Token<"warning10", string, "colors", "hope">;
        warning11: import("@stitches/core/types/theme").Token<"warning11", string, "colors", "hope">;
        warning12: import("@stitches/core/types/theme").Token<"warning12", string, "colors", "hope">;
        info1: import("@stitches/core/types/theme").Token<"info1", string, "colors", "hope">;
        info2: import("@stitches/core/types/theme").Token<"info2", string, "colors", "hope">;
        info3: import("@stitches/core/types/theme").Token<"info3", string, "colors", "hope">;
        info4: import("@stitches/core/types/theme").Token<"info4", string, "colors", "hope">;
        info5: import("@stitches/core/types/theme").Token<"info5", string, "colors", "hope">;
        info6: import("@stitches/core/types/theme").Token<"info6", string, "colors", "hope">;
        info7: import("@stitches/core/types/theme").Token<"info7", string, "colors", "hope">;
        info8: import("@stitches/core/types/theme").Token<"info8", string, "colors", "hope">;
        info9: import("@stitches/core/types/theme").Token<"info9", string, "colors", "hope">;
        info10: import("@stitches/core/types/theme").Token<"info10", string, "colors", "hope">;
        info11: import("@stitches/core/types/theme").Token<"info11", string, "colors", "hope">;
        info12: import("@stitches/core/types/theme").Token<"info12", string, "colors", "hope">;
        success1: import("@stitches/core/types/theme").Token<"success1", string, "colors", "hope">;
        success2: import("@stitches/core/types/theme").Token<"success2", string, "colors", "hope">;
        success3: import("@stitches/core/types/theme").Token<"success3", string, "colors", "hope">;
        success4: import("@stitches/core/types/theme").Token<"success4", string, "colors", "hope">;
        success5: import("@stitches/core/types/theme").Token<"success5", string, "colors", "hope">;
        success6: import("@stitches/core/types/theme").Token<"success6", string, "colors", "hope">;
        success7: import("@stitches/core/types/theme").Token<"success7", string, "colors", "hope">;
        success8: import("@stitches/core/types/theme").Token<"success8", string, "colors", "hope">;
        success9: import("@stitches/core/types/theme").Token<"success9", string, "colors", "hope">;
        success10: import("@stitches/core/types/theme").Token<"success10", string, "colors", "hope">;
        success11: import("@stitches/core/types/theme").Token<"success11", string, "colors", "hope">;
        success12: import("@stitches/core/types/theme").Token<"success12", string, "colors", "hope">;
        neutral1: import("@stitches/core/types/theme").Token<"neutral1", string, "colors", "hope">;
        neutral2: import("@stitches/core/types/theme").Token<"neutral2", string, "colors", "hope">;
        neutral3: import("@stitches/core/types/theme").Token<"neutral3", string, "colors", "hope">;
        neutral4: import("@stitches/core/types/theme").Token<"neutral4", string, "colors", "hope">;
        neutral5: import("@stitches/core/types/theme").Token<"neutral5", string, "colors", "hope">;
        neutral6: import("@stitches/core/types/theme").Token<"neutral6", string, "colors", "hope">;
        neutral7: import("@stitches/core/types/theme").Token<"neutral7", string, "colors", "hope">;
        neutral8: import("@stitches/core/types/theme").Token<"neutral8", string, "colors", "hope">;
        neutral9: import("@stitches/core/types/theme").Token<"neutral9", string, "colors", "hope">;
        neutral10: import("@stitches/core/types/theme").Token<"neutral10", string, "colors", "hope">;
        neutral11: import("@stitches/core/types/theme").Token<"neutral11", string, "colors", "hope">;
        neutral12: import("@stitches/core/types/theme").Token<"neutral12", string, "colors", "hope">;
        accent1: import("@stitches/core/types/theme").Token<"accent1", string, "colors", "hope">;
        accent2: import("@stitches/core/types/theme").Token<"accent2", string, "colors", "hope">;
        accent3: import("@stitches/core/types/theme").Token<"accent3", string, "colors", "hope">;
        accent4: import("@stitches/core/types/theme").Token<"accent4", string, "colors", "hope">;
        accent5: import("@stitches/core/types/theme").Token<"accent5", string, "colors", "hope">;
        accent6: import("@stitches/core/types/theme").Token<"accent6", string, "colors", "hope">;
        accent7: import("@stitches/core/types/theme").Token<"accent7", string, "colors", "hope">;
        accent8: import("@stitches/core/types/theme").Token<"accent8", string, "colors", "hope">;
        accent9: import("@stitches/core/types/theme").Token<"accent9", string, "colors", "hope">;
        accent10: import("@stitches/core/types/theme").Token<"accent10", string, "colors", "hope">;
        accent11: import("@stitches/core/types/theme").Token<"accent11", string, "colors", "hope">;
        accent12: import("@stitches/core/types/theme").Token<"accent12", string, "colors", "hope">;
        primary1: import("@stitches/core/types/theme").Token<"primary1", string, "colors", "hope">;
        primary2: import("@stitches/core/types/theme").Token<"primary2", string, "colors", "hope">;
        primary3: import("@stitches/core/types/theme").Token<"primary3", string, "colors", "hope">;
        primary4: import("@stitches/core/types/theme").Token<"primary4", string, "colors", "hope">;
        primary5: import("@stitches/core/types/theme").Token<"primary5", string, "colors", "hope">;
        primary6: import("@stitches/core/types/theme").Token<"primary6", string, "colors", "hope">;
        primary7: import("@stitches/core/types/theme").Token<"primary7", string, "colors", "hope">;
        primary8: import("@stitches/core/types/theme").Token<"primary8", string, "colors", "hope">;
        primary9: import("@stitches/core/types/theme").Token<"primary9", string, "colors", "hope">;
        primary10: import("@stitches/core/types/theme").Token<"primary10", string, "colors", "hope">;
        primary11: import("@stitches/core/types/theme").Token<"primary11", string, "colors", "hope">;
        primary12: import("@stitches/core/types/theme").Token<"primary12", string, "colors", "hope">;
        whiteAlpha1: import("@stitches/core/types/theme").Token<"whiteAlpha1", string, "colors", "hope">;
        whiteAlpha2: import("@stitches/core/types/theme").Token<"whiteAlpha2", string, "colors", "hope">;
        whiteAlpha3: import("@stitches/core/types/theme").Token<"whiteAlpha3", string, "colors", "hope">;
        whiteAlpha4: import("@stitches/core/types/theme").Token<"whiteAlpha4", string, "colors", "hope">;
        whiteAlpha5: import("@stitches/core/types/theme").Token<"whiteAlpha5", string, "colors", "hope">;
        whiteAlpha6: import("@stitches/core/types/theme").Token<"whiteAlpha6", string, "colors", "hope">;
        whiteAlpha7: import("@stitches/core/types/theme").Token<"whiteAlpha7", string, "colors", "hope">;
        whiteAlpha8: import("@stitches/core/types/theme").Token<"whiteAlpha8", string, "colors", "hope">;
        whiteAlpha9: import("@stitches/core/types/theme").Token<"whiteAlpha9", string, "colors", "hope">;
        whiteAlpha10: import("@stitches/core/types/theme").Token<"whiteAlpha10", string, "colors", "hope">;
        whiteAlpha11: import("@stitches/core/types/theme").Token<"whiteAlpha11", string, "colors", "hope">;
        whiteAlpha12: import("@stitches/core/types/theme").Token<"whiteAlpha12", string, "colors", "hope">;
        blackAlpha1: import("@stitches/core/types/theme").Token<"blackAlpha1", string, "colors", "hope">;
        blackAlpha2: import("@stitches/core/types/theme").Token<"blackAlpha2", string, "colors", "hope">;
        blackAlpha3: import("@stitches/core/types/theme").Token<"blackAlpha3", string, "colors", "hope">;
        blackAlpha4: import("@stitches/core/types/theme").Token<"blackAlpha4", string, "colors", "hope">;
        blackAlpha5: import("@stitches/core/types/theme").Token<"blackAlpha5", string, "colors", "hope">;
        blackAlpha6: import("@stitches/core/types/theme").Token<"blackAlpha6", string, "colors", "hope">;
        blackAlpha7: import("@stitches/core/types/theme").Token<"blackAlpha7", string, "colors", "hope">;
        blackAlpha8: import("@stitches/core/types/theme").Token<"blackAlpha8", string, "colors", "hope">;
        blackAlpha9: import("@stitches/core/types/theme").Token<"blackAlpha9", string, "colors", "hope">;
        blackAlpha10: import("@stitches/core/types/theme").Token<"blackAlpha10", string, "colors", "hope">;
        blackAlpha11: import("@stitches/core/types/theme").Token<"blackAlpha11", string, "colors", "hope">;
        blackAlpha12: import("@stitches/core/types/theme").Token<"blackAlpha12", string, "colors", "hope">;
    };
    space: {
        px: import("@stitches/core/types/theme").Token<"px", string, "space", "hope">;
        "0_5": import("@stitches/core/types/theme").Token<"0_5", string, "space", "hope">;
        1: import("@stitches/core/types/theme").Token<"1", string, "space", "hope">;
        "1_5": import("@stitches/core/types/theme").Token<"1_5", string, "space", "hope">;
        2: import("@stitches/core/types/theme").Token<"2", string, "space", "hope">;
        "2_5": import("@stitches/core/types/theme").Token<"2_5", string, "space", "hope">;
        3: import("@stitches/core/types/theme").Token<"3", string, "space", "hope">;
        "3_5": import("@stitches/core/types/theme").Token<"3_5", string, "space", "hope">;
        4: import("@stitches/core/types/theme").Token<"4", string, "space", "hope">;
        5: import("@stitches/core/types/theme").Token<"5", string, "space", "hope">;
        6: import("@stitches/core/types/theme").Token<"6", string, "space", "hope">;
        7: import("@stitches/core/types/theme").Token<"7", string, "space", "hope">;
        8: import("@stitches/core/types/theme").Token<"8", string, "space", "hope">;
        9: import("@stitches/core/types/theme").Token<"9", string, "space", "hope">;
        10: import("@stitches/core/types/theme").Token<"10", string, "space", "hope">;
        12: import("@stitches/core/types/theme").Token<"12", string, "space", "hope">;
        14: import("@stitches/core/types/theme").Token<"14", string, "space", "hope">;
        16: import("@stitches/core/types/theme").Token<"16", string, "space", "hope">;
        20: import("@stitches/core/types/theme").Token<"20", string, "space", "hope">;
        24: import("@stitches/core/types/theme").Token<"24", string, "space", "hope">;
        28: import("@stitches/core/types/theme").Token<"28", string, "space", "hope">;
        32: import("@stitches/core/types/theme").Token<"32", string, "space", "hope">;
        36: import("@stitches/core/types/theme").Token<"36", string, "space", "hope">;
        40: import("@stitches/core/types/theme").Token<"40", string, "space", "hope">;
        44: import("@stitches/core/types/theme").Token<"44", string, "space", "hope">;
        48: import("@stitches/core/types/theme").Token<"48", string, "space", "hope">;
        52: import("@stitches/core/types/theme").Token<"52", string, "space", "hope">;
        56: import("@stitches/core/types/theme").Token<"56", string, "space", "hope">;
        60: import("@stitches/core/types/theme").Token<"60", string, "space", "hope">;
        64: import("@stitches/core/types/theme").Token<"64", string, "space", "hope">;
        72: import("@stitches/core/types/theme").Token<"72", string, "space", "hope">;
        80: import("@stitches/core/types/theme").Token<"80", string, "space", "hope">;
        96: import("@stitches/core/types/theme").Token<"96", string, "space", "hope">;
    };
    sizes: {
        prose: import("@stitches/core/types/theme").Token<"prose", string, "sizes", "hope">;
        max: import("@stitches/core/types/theme").Token<"max", string, "sizes", "hope">;
        min: import("@stitches/core/types/theme").Token<"min", string, "sizes", "hope">;
        full: import("@stitches/core/types/theme").Token<"full", string, "sizes", "hope">;
        screenW: import("@stitches/core/types/theme").Token<"screenW", string, "sizes", "hope">;
        screenH: import("@stitches/core/types/theme").Token<"screenH", string, "sizes", "hope">;
        xs: import("@stitches/core/types/theme").Token<"xs", string, "sizes", "hope">;
        sm: import("@stitches/core/types/theme").Token<"sm", string, "sizes", "hope">;
        md: import("@stitches/core/types/theme").Token<"md", string, "sizes", "hope">;
        lg: import("@stitches/core/types/theme").Token<"lg", string, "sizes", "hope">;
        xl: import("@stitches/core/types/theme").Token<"xl", string, "sizes", "hope">;
        "2xl": import("@stitches/core/types/theme").Token<"2xl", string, "sizes", "hope">;
        "3xl": import("@stitches/core/types/theme").Token<"3xl", string, "sizes", "hope">;
        "4xl": import("@stitches/core/types/theme").Token<"4xl", string, "sizes", "hope">;
        "5xl": import("@stitches/core/types/theme").Token<"5xl", string, "sizes", "hope">;
        "6xl": import("@stitches/core/types/theme").Token<"6xl", string, "sizes", "hope">;
        "7xl": import("@stitches/core/types/theme").Token<"7xl", string, "sizes", "hope">;
        "8xl": import("@stitches/core/types/theme").Token<"8xl", string, "sizes", "hope">;
        containerSm: import("@stitches/core/types/theme").Token<"containerSm", string, "sizes", "hope">;
        containerMd: import("@stitches/core/types/theme").Token<"containerMd", string, "sizes", "hope">;
        containerLg: import("@stitches/core/types/theme").Token<"containerLg", string, "sizes", "hope">;
        containerXl: import("@stitches/core/types/theme").Token<"containerXl", string, "sizes", "hope">;
        container2xl: import("@stitches/core/types/theme").Token<"container2xl", string, "sizes", "hope">;
        px: import("@stitches/core/types/theme").Token<"px", string, "sizes", "hope">;
        "0_5": import("@stitches/core/types/theme").Token<"0_5", string, "sizes", "hope">;
        1: import("@stitches/core/types/theme").Token<"1", string, "sizes", "hope">;
        "1_5": import("@stitches/core/types/theme").Token<"1_5", string, "sizes", "hope">;
        2: import("@stitches/core/types/theme").Token<"2", string, "sizes", "hope">;
        "2_5": import("@stitches/core/types/theme").Token<"2_5", string, "sizes", "hope">;
        3: import("@stitches/core/types/theme").Token<"3", string, "sizes", "hope">;
        "3_5": import("@stitches/core/types/theme").Token<"3_5", string, "sizes", "hope">;
        4: import("@stitches/core/types/theme").Token<"4", string, "sizes", "hope">;
        5: import("@stitches/core/types/theme").Token<"5", string, "sizes", "hope">;
        6: import("@stitches/core/types/theme").Token<"6", string, "sizes", "hope">;
        7: import("@stitches/core/types/theme").Token<"7", string, "sizes", "hope">;
        8: import("@stitches/core/types/theme").Token<"8", string, "sizes", "hope">;
        9: import("@stitches/core/types/theme").Token<"9", string, "sizes", "hope">;
        10: import("@stitches/core/types/theme").Token<"10", string, "sizes", "hope">;
        12: import("@stitches/core/types/theme").Token<"12", string, "sizes", "hope">;
        14: import("@stitches/core/types/theme").Token<"14", string, "sizes", "hope">;
        16: import("@stitches/core/types/theme").Token<"16", string, "sizes", "hope">;
        20: import("@stitches/core/types/theme").Token<"20", string, "sizes", "hope">;
        24: import("@stitches/core/types/theme").Token<"24", string, "sizes", "hope">;
        28: import("@stitches/core/types/theme").Token<"28", string, "sizes", "hope">;
        32: import("@stitches/core/types/theme").Token<"32", string, "sizes", "hope">;
        36: import("@stitches/core/types/theme").Token<"36", string, "sizes", "hope">;
        40: import("@stitches/core/types/theme").Token<"40", string, "sizes", "hope">;
        44: import("@stitches/core/types/theme").Token<"44", string, "sizes", "hope">;
        48: import("@stitches/core/types/theme").Token<"48", string, "sizes", "hope">;
        52: import("@stitches/core/types/theme").Token<"52", string, "sizes", "hope">;
        56: import("@stitches/core/types/theme").Token<"56", string, "sizes", "hope">;
        60: import("@stitches/core/types/theme").Token<"60", string, "sizes", "hope">;
        64: import("@stitches/core/types/theme").Token<"64", string, "sizes", "hope">;
        72: import("@stitches/core/types/theme").Token<"72", string, "sizes", "hope">;
        80: import("@stitches/core/types/theme").Token<"80", string, "sizes", "hope">;
        96: import("@stitches/core/types/theme").Token<"96", string, "sizes", "hope">;
    };
    fonts: {
        sans: import("@stitches/core/types/theme").Token<"sans", string, "fonts", "hope">;
        serif: import("@stitches/core/types/theme").Token<"serif", string, "fonts", "hope">;
        mono: import("@stitches/core/types/theme").Token<"mono", string, "fonts", "hope">;
    };
    fontSizes: {
        "2xs": import("@stitches/core/types/theme").Token<"2xs", string, "fontSizes", "hope">;
        xs: import("@stitches/core/types/theme").Token<"xs", string, "fontSizes", "hope">;
        sm: import("@stitches/core/types/theme").Token<"sm", string, "fontSizes", "hope">;
        base: import("@stitches/core/types/theme").Token<"base", string, "fontSizes", "hope">;
        lg: import("@stitches/core/types/theme").Token<"lg", string, "fontSizes", "hope">;
        xl: import("@stitches/core/types/theme").Token<"xl", string, "fontSizes", "hope">;
        "2xl": import("@stitches/core/types/theme").Token<"2xl", string, "fontSizes", "hope">;
        "3xl": import("@stitches/core/types/theme").Token<"3xl", string, "fontSizes", "hope">;
        "4xl": import("@stitches/core/types/theme").Token<"4xl", string, "fontSizes", "hope">;
        "5xl": import("@stitches/core/types/theme").Token<"5xl", string, "fontSizes", "hope">;
        "6xl": import("@stitches/core/types/theme").Token<"6xl", string, "fontSizes", "hope">;
        "7xl": import("@stitches/core/types/theme").Token<"7xl", string, "fontSizes", "hope">;
        "8xl": import("@stitches/core/types/theme").Token<"8xl", string, "fontSizes", "hope">;
        "9xl": import("@stitches/core/types/theme").Token<"9xl", string, "fontSizes", "hope">;
    };
    fontWeights: {
        hairline: import("@stitches/core/types/theme").Token<"hairline", string, "fontWeights", "hope">;
        thin: import("@stitches/core/types/theme").Token<"thin", string, "fontWeights", "hope">;
        light: import("@stitches/core/types/theme").Token<"light", string, "fontWeights", "hope">;
        normal: import("@stitches/core/types/theme").Token<"normal", string, "fontWeights", "hope">;
        medium: import("@stitches/core/types/theme").Token<"medium", string, "fontWeights", "hope">;
        semibold: import("@stitches/core/types/theme").Token<"semibold", string, "fontWeights", "hope">;
        bold: import("@stitches/core/types/theme").Token<"bold", string, "fontWeights", "hope">;
        extrabold: import("@stitches/core/types/theme").Token<"extrabold", string, "fontWeights", "hope">;
        black: import("@stitches/core/types/theme").Token<"black", string, "fontWeights", "hope">;
    };
    letterSpacings: {
        tighter: import("@stitches/core/types/theme").Token<"tighter", string, "letterSpacings", "hope">;
        tight: import("@stitches/core/types/theme").Token<"tight", string, "letterSpacings", "hope">;
        normal: import("@stitches/core/types/theme").Token<"normal", string, "letterSpacings", "hope">;
        wide: import("@stitches/core/types/theme").Token<"wide", string, "letterSpacings", "hope">;
        wider: import("@stitches/core/types/theme").Token<"wider", string, "letterSpacings", "hope">;
        widest: import("@stitches/core/types/theme").Token<"widest", string, "letterSpacings", "hope">;
    };
    lineHeights: {
        normal: import("@stitches/core/types/theme").Token<"normal", string, "lineHeights", "hope">;
        none: import("@stitches/core/types/theme").Token<"none", string, "lineHeights", "hope">;
        shorter: import("@stitches/core/types/theme").Token<"shorter", string, "lineHeights", "hope">;
        short: import("@stitches/core/types/theme").Token<"short", string, "lineHeights", "hope">;
        base: import("@stitches/core/types/theme").Token<"base", string, "lineHeights", "hope">;
        tall: import("@stitches/core/types/theme").Token<"tall", string, "lineHeights", "hope">;
        taller: import("@stitches/core/types/theme").Token<"taller", string, "lineHeights", "hope">;
        3: import("@stitches/core/types/theme").Token<"3", string, "lineHeights", "hope">;
        4: import("@stitches/core/types/theme").Token<"4", string, "lineHeights", "hope">;
        5: import("@stitches/core/types/theme").Token<"5", string, "lineHeights", "hope">;
        6: import("@stitches/core/types/theme").Token<"6", string, "lineHeights", "hope">;
        7: import("@stitches/core/types/theme").Token<"7", string, "lineHeights", "hope">;
        8: import("@stitches/core/types/theme").Token<"8", string, "lineHeights", "hope">;
        9: import("@stitches/core/types/theme").Token<"9", string, "lineHeights", "hope">;
        10: import("@stitches/core/types/theme").Token<"10", string, "lineHeights", "hope">;
    };
    radii: {
        none: import("@stitches/core/types/theme").Token<"none", string, "radii", "hope">;
        xs: import("@stitches/core/types/theme").Token<"xs", string, "radii", "hope">;
        sm: import("@stitches/core/types/theme").Token<"sm", string, "radii", "hope">;
        md: import("@stitches/core/types/theme").Token<"md", string, "radii", "hope">;
        lg: import("@stitches/core/types/theme").Token<"lg", string, "radii", "hope">;
        xl: import("@stitches/core/types/theme").Token<"xl", string, "radii", "hope">;
        "2xl": import("@stitches/core/types/theme").Token<"2xl", string, "radii", "hope">;
        "3xl": import("@stitches/core/types/theme").Token<"3xl", string, "radii", "hope">;
        full: import("@stitches/core/types/theme").Token<"full", string, "radii", "hope">;
    };
    shadows: {
        none: import("@stitches/core/types/theme").Token<"none", string, "shadows", "hope">;
        xs: import("@stitches/core/types/theme").Token<"xs", string, "shadows", "hope">;
        sm: import("@stitches/core/types/theme").Token<"sm", string, "shadows", "hope">;
        md: import("@stitches/core/types/theme").Token<"md", string, "shadows", "hope">;
        lg: import("@stitches/core/types/theme").Token<"lg", string, "shadows", "hope">;
        xl: import("@stitches/core/types/theme").Token<"xl", string, "shadows", "hope">;
        "2xl": import("@stitches/core/types/theme").Token<"2xl", string, "shadows", "hope">;
        inner: import("@stitches/core/types/theme").Token<"inner", string, "shadows", "hope">;
        outline: import("@stitches/core/types/theme").Token<"outline", string, "shadows", "hope">;
    };
    zIndices: {
        hide: import("@stitches/core/types/theme").Token<"hide", string, "zIndices", "hope">;
        auto: import("@stitches/core/types/theme").Token<"auto", string, "zIndices", "hope">;
        base: import("@stitches/core/types/theme").Token<"base", string, "zIndices", "hope">;
        docked: import("@stitches/core/types/theme").Token<"docked", string, "zIndices", "hope">;
        sticky: import("@stitches/core/types/theme").Token<"sticky", string, "zIndices", "hope">;
        banner: import("@stitches/core/types/theme").Token<"banner", string, "zIndices", "hope">;
        overlay: import("@stitches/core/types/theme").Token<"overlay", string, "zIndices", "hope">;
        modal: import("@stitches/core/types/theme").Token<"modal", string, "zIndices", "hope">;
        dropdown: import("@stitches/core/types/theme").Token<"dropdown", string, "zIndices", "hope">;
        popover: import("@stitches/core/types/theme").Token<"popover", string, "zIndices", "hope">;
        tooltip: import("@stitches/core/types/theme").Token<"tooltip", string, "zIndices", "hope">;
        skipLink: import("@stitches/core/types/theme").Token<"skipLink", string, "zIndices", "hope">;
        notification: import("@stitches/core/types/theme").Token<"notification", string, "zIndices", "hope">;
    };
}, css: <Composers extends (string | import("@stitches/core/types/util").Function | {
    [name: string]: unknown;
})[], CSS = import("@stitches/core/types/css-util").CSS<{
    sm: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
    "reduce-motion": string;
    light: string;
    dark: string;
}, {
    colors: {
        loContrast: string;
        background: string;
        focusRing: string;
        closeButtonHoverBackground: string;
        closeButtonActiveBackground: string;
        progressStripe: string;
        danger1: string;
        danger2: string;
        danger3: string;
        danger4: string;
        danger5: string;
        danger6: string;
        danger7: string;
        danger8: string;
        danger9: string;
        danger10: string;
        danger11: string;
        danger12: string;
        warning1: string;
        warning2: string;
        warning3: string;
        warning4: string;
        warning5: string;
        warning6: string;
        warning7: string;
        warning8: string;
        warning9: string;
        warning10: string;
        warning11: string;
        warning12: string;
        info1: string;
        info2: string;
        info3: string;
        info4: string;
        info5: string;
        info6: string;
        info7: string;
        info8: string;
        info9: string;
        info10: string;
        info11: string;
        info12: string;
        success1: string;
        success2: string;
        success3: string;
        success4: string;
        success5: string;
        success6: string;
        success7: string;
        success8: string;
        success9: string;
        success10: string;
        success11: string;
        success12: string;
        neutral1: string;
        neutral2: string;
        neutral3: string;
        neutral4: string;
        neutral5: string;
        neutral6: string;
        neutral7: string;
        neutral8: string;
        neutral9: string;
        neutral10: string;
        neutral11: string;
        neutral12: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
        accent5: string;
        accent6: string;
        accent7: string;
        accent8: string;
        accent9: string;
        accent10: string;
        accent11: string;
        accent12: string;
        primary1: string;
        primary2: string;
        primary3: string;
        primary4: string;
        primary5: string;
        primary6: string;
        primary7: string;
        primary8: string;
        primary9: string;
        primary10: string;
        primary11: string;
        primary12: string;
        whiteAlpha1: string;
        whiteAlpha2: string;
        whiteAlpha3: string;
        whiteAlpha4: string;
        whiteAlpha5: string;
        whiteAlpha6: string;
        whiteAlpha7: string;
        whiteAlpha8: string;
        whiteAlpha9: string;
        whiteAlpha10: string;
        whiteAlpha11: string;
        whiteAlpha12: string;
        blackAlpha1: string;
        blackAlpha2: string;
        blackAlpha3: string;
        blackAlpha4: string;
        blackAlpha5: string;
        blackAlpha6: string;
        blackAlpha7: string;
        blackAlpha8: string;
        blackAlpha9: string;
        blackAlpha10: string;
        blackAlpha11: string;
        blackAlpha12: string;
    };
    space: {
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    sizes: {
        prose: string;
        max: string;
        min: string;
        full: string;
        screenW: string;
        screenH: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        containerSm: string;
        containerMd: string;
        containerLg: string;
        containerXl: string;
        container2xl: string;
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    fonts: {
        sans: string;
        serif: string;
        mono: string;
    };
    fontSizes: {
        "2xs": string;
        xs: string;
        sm: string;
        base: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        "9xl": string;
    };
    fontWeights: {
        hairline: number;
        thin: number;
        light: number;
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
        extrabold: number;
        black: number;
    };
    letterSpacings: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeights: {
        normal: string;
        none: number;
        shorter: number;
        short: number;
        base: number;
        tall: number;
        taller: number;
        "3": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
    };
    radii: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        full: string;
    };
    shadows: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        inner: string;
        outline: string;
    };
    zIndices: {
        hide: number;
        auto: string;
        base: number;
        docked: number;
        sticky: number;
        banner: number;
        overlay: number;
        modal: number;
        dropdown: number;
        popover: number;
        tooltip: number;
        skipLink: number;
        notification: number;
    };
}, {
    borderWidth: "sizes";
    borderTopWidth: "sizes";
    borderRightWidth: "sizes";
    borderBottomWidth: "sizes";
    borderLeftWidth: "sizes";
    strokeWidth: "sizes";
    gap: "space";
    gridGap: "space";
    columnGap: "space";
    gridColumnGap: "space";
    rowGap: "space";
    gridRowGap: "space";
    inset: "space";
    insetBlock: "space";
    insetBlockEnd: "space";
    insetBlockStart: "space";
    insetInline: "space";
    insetInlineEnd: "space";
    insetInlineStart: "space";
    margin: "space";
    marginTop: "space";
    marginRight: "space";
    marginBottom: "space";
    marginLeft: "space";
    marginBlock: "space";
    marginBlockEnd: "space";
    marginBlockStart: "space";
    marginInline: "space";
    marginInlineEnd: "space";
    marginInlineStart: "space";
    padding: "space";
    paddingTop: "space";
    paddingRight: "space";
    paddingBottom: "space";
    paddingLeft: "space";
    paddingBlock: "space";
    paddingBlockEnd: "space";
    paddingBlockStart: "space";
    paddingInline: "space";
    paddingInlineEnd: "space";
    paddingInlineStart: "space";
    scrollMargin: "space";
    scrollMarginTop: "space";
    scrollMarginRight: "space";
    scrollMarginBottom: "space";
    scrollMarginLeft: "space";
    scrollMarginBlock: "space";
    scrollMarginBlockEnd: "space";
    scrollMarginBlockStart: "space";
    scrollMarginInline: "space";
    scrollMarginInlineEnd: "space";
    scrollMarginInlineStart: "space";
    scrollPadding: "space";
    scrollPaddingTop: "space";
    scrollPaddingRight: "space";
    scrollPaddingBottom: "space";
    scrollPaddingLeft: "space";
    scrollPaddingBlock: "space";
    scrollPaddingBlockEnd: "space";
    scrollPaddingBlockStart: "space";
    scrollPaddingInline: "space";
    scrollPaddingInlineEnd: "space";
    scrollPaddingInlineStart: "space";
    top: "space";
    right: "space";
    bottom: "space";
    left: "space";
    fontSize: "fontSizes";
    background: "colors";
    backgroundColor: "colors";
    backgroundImage: "colors";
    borderImage: "colors";
    border: "colors";
    borderBlock: "colors";
    borderBlockEnd: "colors";
    borderBlockStart: "colors";
    borderBottom: "colors";
    borderBottomColor: "colors";
    borderColor: "colors";
    borderInline: "colors";
    borderInlineEnd: "colors";
    borderInlineStart: "colors";
    borderLeft: "colors";
    borderLeftColor: "colors";
    borderRight: "colors";
    borderRightColor: "colors";
    borderTop: "colors";
    borderTopColor: "colors";
    caretColor: "colors";
    color: "colors";
    columnRuleColor: "colors";
    outline: "colors";
    outlineColor: "colors";
    fill: "colors";
    stroke: "colors";
    textDecorationColor: "colors";
    fontFamily: "fonts";
    fontWeight: "fontWeights";
    lineHeight: "lineHeights";
    letterSpacing: "letterSpacings";
    blockSize: "sizes";
    minBlockSize: "sizes";
    maxBlockSize: "sizes";
    inlineSize: "sizes";
    minInlineSize: "sizes";
    maxInlineSize: "sizes";
    width: "sizes";
    minWidth: "sizes";
    maxWidth: "sizes";
    height: "sizes";
    minHeight: "sizes";
    maxHeight: "sizes";
    flexBasis: "sizes";
    gridTemplateColumns: "sizes";
    gridTemplateRows: "sizes";
    borderStyle: "borderStyles";
    borderTopStyle: "borderStyles";
    borderLeftStyle: "borderStyles";
    borderRightStyle: "borderStyles";
    borderBottomStyle: "borderStyles";
    borderRadius: "radii";
    borderTopLeftRadius: "radii";
    borderTopRightRadius: "radii";
    borderBottomRightRadius: "radii";
    borderBottomLeftRadius: "radii";
    boxShadow: "shadows";
    textShadow: "shadows";
    transition: "transitions";
    zIndex: "zIndices";
}, {
    noOfLines: (value: string | number) => {
        overflow: string;
        display: string;
        "-webkit-box-orient": string;
        "-webkit-line-clamp": string | number;
    };
    w: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
    };
    minW: (value: {
        readonly [$$PropertyValue]: "minWidth";
    }) => {
        minWidth: {
            readonly [$$PropertyValue]: "minWidth";
        };
    };
    maxW: (value: {
        readonly [$$PropertyValue]: "maxWidth";
    }) => {
        maxWidth: {
            readonly [$$PropertyValue]: "maxWidth";
        };
    };
    h: (value: {
        readonly [$$PropertyValue]: "height";
    }) => {
        height: {
            readonly [$$PropertyValue]: "height";
        };
    };
    minH: (value: {
        readonly [$$PropertyValue]: "minHeight";
    }) => {
        minHeight: {
            readonly [$$PropertyValue]: "minHeight";
        };
    };
    maxH: (value: {
        readonly [$$PropertyValue]: "maxHeight";
    }) => {
        maxHeight: {
            readonly [$$PropertyValue]: "maxHeight";
        };
    };
    boxSize: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
        height: {
            readonly [$$PropertyValue]: "width";
        };
    };
    shadow: (value: {
        readonly [$$PropertyValue]: "boxShadow";
    }) => {
        boxShadow: {
            readonly [$$PropertyValue]: "boxShadow";
        };
    };
    p: (value: {
        readonly [$$PropertyValue]: "padding";
    }) => {
        padding: {
            readonly [$$PropertyValue]: "padding";
        };
    };
    pt: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    pr: (value: {
        readonly [$$PropertyValue]: "paddingRight";
    }) => {
        paddingRight: {
            readonly [$$PropertyValue]: "paddingRight";
        };
    };
    paddingStart: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    ps: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    pb: (value: {
        readonly [$$PropertyValue]: "paddingBottom";
    }) => {
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingBottom";
        };
    };
    pl: (value: {
        readonly [$$PropertyValue]: "paddingLeft";
    }) => {
        paddingLeft: {
            readonly [$$PropertyValue]: "paddingLeft";
        };
    };
    pe: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    paddingEnd: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    px: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    py: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    m: (value: {
        readonly [$$PropertyValue]: "margin";
    }) => {
        margin: {
            readonly [$$PropertyValue]: "margin";
        };
    };
    mt: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    mr: (value: {
        readonly [$$PropertyValue]: "marginRight";
    }) => {
        marginRight: {
            readonly [$$PropertyValue]: "marginRight";
        };
    };
    marginStart: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    ms: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    mb: (value: {
        readonly [$$PropertyValue]: "marginBottom";
    }) => {
        marginBottom: {
            readonly [$$PropertyValue]: "marginBottom";
        };
    };
    ml: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        marginLeft: {
            readonly [$$PropertyValue]: "marginLeft";
        };
    };
    marginEnd: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    me: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    mx: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    my: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
        marginBottom: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    spaceX: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        "& > * + *": {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
    };
    spaceY: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        "& > * + *": {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
    };
    borderTopRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderRightRadius: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    borderStartRadius: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    borderBottomRadius: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    borderLeftRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderEndRadius: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    rounded: (value: {
        readonly [$$PropertyValue]: "borderRadius";
    }) => {
        borderRadius: {
            readonly [$$PropertyValue]: "borderRadius";
        };
    };
    roundedTop: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedRight: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    roundedStart: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    roundedBottom: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    roundedLeft: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedEnd: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    _hover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:hover, &[data-hover]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _active: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:active, &[data-active]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus, &[data-focus]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _highlighted: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-highlighted]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-within": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-visible": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _disabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _readOnly: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _before: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::before": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _after: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::after": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _empty: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:empty": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _expanded: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _checked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _grabbed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _pressed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _invalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _valid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _loading: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _hidden: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _even: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(even)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _odd: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(odd)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _first: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:first-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _last: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:last-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notFirst: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:first-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notLast: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:last-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _visited: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:visited": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeLink: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=page]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeStep: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=step]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _indeterminate: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholder: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::placeholder": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:placeholder-shown": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _fullScreen: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:fullscreen": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selection: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::selection": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaDark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaReduceMotion: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _dark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-dark &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _light: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-light &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    pos: (value: {
        readonly [$$PropertyValue]: "position";
    }) => {
        position: {
            readonly [$$PropertyValue]: "position";
        };
    };
    d: (value: {
        readonly [$$PropertyValue]: "display";
    }) => {
        display: {
            readonly [$$PropertyValue]: "display";
        };
    };
    borderX: (value: {
        readonly [$$PropertyValue]: "borderLeft";
    }) => {
        borderLeft: {
            readonly [$$PropertyValue]: "borderLeft";
        };
        borderRight: {
            readonly [$$PropertyValue]: "borderLeft";
        };
    };
    borderY: (value: {
        readonly [$$PropertyValue]: "borderTop";
    }) => {
        borderTop: {
            readonly [$$PropertyValue]: "borderTop";
        };
        borderBottom: {
            readonly [$$PropertyValue]: "borderTop";
        };
    };
    bg: (value: {
        readonly [$$PropertyValue]: "background";
    }) => {
        background: {
            readonly [$$PropertyValue]: "background";
        };
    };
    bgColor: (value: {
        readonly [$$PropertyValue]: "backgroundColor";
    }) => {
        backgroundColor: {
            readonly [$$PropertyValue]: "backgroundColor";
        };
    };
}>>(...composers: { [K in keyof Composers]: string extends Composers[K] ? Composers[K] : Composers[K] extends string | import("@stitches/core/types/util").Function ? Composers[K] : import("@stitches/core/types/stitches").RemoveIndex<CSS> & {
    variants?: {
        [x: string]: {
            [x: string]: CSS;
            [x: number]: CSS;
        };
    } | undefined;
    compoundVariants?: (("variants" extends keyof Composers[K] ? { [Name in keyof Composers[K][keyof Composers[K] & "variants"]]?: import("@stitches/core/types/util").String | import("@stitches/core/types/util").Widen<keyof Composers[K][keyof Composers[K] & "variants"][Name]> | undefined; } : import("@stitches/core/types/util").WideObject) & {
        css: CSS;
    })[] | undefined;
    defaultVariants?: ("variants" extends keyof Composers[K] ? { [Name_1 in keyof Composers[K][keyof Composers[K] & "variants"]]?: import("@stitches/core/types/util").String | import("@stitches/core/types/util").Widen<keyof Composers[K][keyof Composers[K] & "variants"][Name_1]> | undefined; } : import("@stitches/core/types/util").WideObject) | undefined;
} & CSS & { [K2 in keyof Composers[K]]: K2 extends "compoundVariants" | "defaultVariants" | "variants" ? unknown : K2 extends keyof CSS ? CSS[K2] : unknown; }; }) => import("@stitches/core/types/styled-component").CssComponent<import("@stitches/core/types/styled-component").StyledComponentType<Composers>, import("@stitches/core/types/styled-component").StyledComponentProps<Composers>, {
    sm: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
    "reduce-motion": string;
    light: string;
    dark: string;
}, CSS>, globalCss: <Styles extends {
    [K: string]: any;
}>(...styles: ({
    '@import'?: unknown;
    '@font-face'?: unknown;
} & { [K in keyof Styles]: K extends "@@font-face" ? import("@stitches/core/types/css").AtRule.FontFace | import("@stitches/core/types/css").AtRule.FontFace[] : K extends `@keyframes ${string}` ? {
    [x: string]: import("@stitches/core/types/css-util").CSS<{
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "reduce-motion": string;
        light: string;
        dark: string;
    }, {
        colors: {
            loContrast: string;
            background: string;
            focusRing: string;
            closeButtonHoverBackground: string;
            closeButtonActiveBackground: string;
            progressStripe: string;
            danger1: string;
            danger2: string;
            danger3: string;
            danger4: string;
            danger5: string;
            danger6: string;
            danger7: string;
            danger8: string;
            danger9: string;
            danger10: string;
            danger11: string;
            danger12: string;
            warning1: string;
            warning2: string;
            warning3: string;
            warning4: string;
            warning5: string;
            warning6: string;
            warning7: string;
            warning8: string;
            warning9: string;
            warning10: string;
            warning11: string;
            warning12: string;
            info1: string;
            info2: string;
            info3: string;
            info4: string;
            info5: string;
            info6: string;
            info7: string;
            info8: string;
            info9: string;
            info10: string;
            info11: string;
            info12: string;
            success1: string;
            success2: string;
            success3: string;
            success4: string;
            success5: string;
            success6: string;
            success7: string;
            success8: string;
            success9: string;
            success10: string;
            success11: string;
            success12: string;
            neutral1: string;
            neutral2: string;
            neutral3: string;
            neutral4: string;
            neutral5: string;
            neutral6: string;
            neutral7: string;
            neutral8: string;
            neutral9: string;
            neutral10: string;
            neutral11: string;
            neutral12: string;
            accent1: string;
            accent2: string;
            accent3: string;
            accent4: string;
            accent5: string;
            accent6: string;
            accent7: string;
            accent8: string;
            accent9: string;
            accent10: string;
            accent11: string;
            accent12: string;
            primary1: string;
            primary2: string;
            primary3: string;
            primary4: string;
            primary5: string;
            primary6: string;
            primary7: string;
            primary8: string;
            primary9: string;
            primary10: string;
            primary11: string;
            primary12: string;
            whiteAlpha1: string;
            whiteAlpha2: string;
            whiteAlpha3: string;
            whiteAlpha4: string;
            whiteAlpha5: string;
            whiteAlpha6: string;
            whiteAlpha7: string;
            whiteAlpha8: string;
            whiteAlpha9: string;
            whiteAlpha10: string;
            whiteAlpha11: string;
            whiteAlpha12: string;
            blackAlpha1: string;
            blackAlpha2: string;
            blackAlpha3: string;
            blackAlpha4: string;
            blackAlpha5: string;
            blackAlpha6: string;
            blackAlpha7: string;
            blackAlpha8: string;
            blackAlpha9: string;
            blackAlpha10: string;
            blackAlpha11: string;
            blackAlpha12: string;
        };
        space: {
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        sizes: {
            prose: string;
            max: string;
            min: string;
            full: string;
            screenW: string;
            screenH: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            containerSm: string;
            containerMd: string;
            containerLg: string;
            containerXl: string;
            container2xl: string;
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        fonts: {
            sans: string;
            serif: string;
            mono: string;
        };
        fontSizes: {
            "2xs": string;
            xs: string;
            sm: string;
            base: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            "9xl": string;
        };
        fontWeights: {
            hairline: number;
            thin: number;
            light: number;
            normal: number;
            medium: number;
            semibold: number;
            bold: number;
            extrabold: number;
            black: number;
        };
        letterSpacings: {
            tighter: string;
            tight: string;
            normal: string;
            wide: string;
            wider: string;
            widest: string;
        };
        lineHeights: {
            normal: string;
            none: number;
            shorter: number;
            short: number;
            base: number;
            tall: number;
            taller: number;
            "3": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
        };
        radii: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            full: string;
        };
        shadows: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            inner: string;
            outline: string;
        };
        zIndices: {
            hide: number;
            auto: string;
            base: number;
            docked: number;
            sticky: number;
            banner: number;
            overlay: number;
            modal: number;
            dropdown: number;
            popover: number;
            tooltip: number;
            skipLink: number;
            notification: number;
        };
    }, {
        borderWidth: "sizes";
        borderTopWidth: "sizes";
        borderRightWidth: "sizes";
        borderBottomWidth: "sizes";
        borderLeftWidth: "sizes";
        strokeWidth: "sizes";
        gap: "space";
        gridGap: "space";
        columnGap: "space";
        gridColumnGap: "space";
        rowGap: "space";
        gridRowGap: "space";
        inset: "space";
        insetBlock: "space";
        insetBlockEnd: "space";
        insetBlockStart: "space";
        insetInline: "space";
        insetInlineEnd: "space";
        insetInlineStart: "space";
        margin: "space";
        marginTop: "space";
        marginRight: "space";
        marginBottom: "space";
        marginLeft: "space";
        marginBlock: "space";
        marginBlockEnd: "space";
        marginBlockStart: "space";
        marginInline: "space";
        marginInlineEnd: "space";
        marginInlineStart: "space";
        padding: "space";
        paddingTop: "space";
        paddingRight: "space";
        paddingBottom: "space";
        paddingLeft: "space";
        paddingBlock: "space";
        paddingBlockEnd: "space";
        paddingBlockStart: "space";
        paddingInline: "space";
        paddingInlineEnd: "space";
        paddingInlineStart: "space";
        scrollMargin: "space";
        scrollMarginTop: "space";
        scrollMarginRight: "space";
        scrollMarginBottom: "space";
        scrollMarginLeft: "space";
        scrollMarginBlock: "space";
        scrollMarginBlockEnd: "space";
        scrollMarginBlockStart: "space";
        scrollMarginInline: "space";
        scrollMarginInlineEnd: "space";
        scrollMarginInlineStart: "space";
        scrollPadding: "space";
        scrollPaddingTop: "space";
        scrollPaddingRight: "space";
        scrollPaddingBottom: "space";
        scrollPaddingLeft: "space";
        scrollPaddingBlock: "space";
        scrollPaddingBlockEnd: "space";
        scrollPaddingBlockStart: "space";
        scrollPaddingInline: "space";
        scrollPaddingInlineEnd: "space";
        scrollPaddingInlineStart: "space";
        top: "space";
        right: "space";
        bottom: "space";
        left: "space";
        fontSize: "fontSizes";
        background: "colors";
        backgroundColor: "colors";
        backgroundImage: "colors";
        borderImage: "colors";
        border: "colors";
        borderBlock: "colors";
        borderBlockEnd: "colors";
        borderBlockStart: "colors";
        borderBottom: "colors";
        borderBottomColor: "colors";
        borderColor: "colors";
        borderInline: "colors";
        borderInlineEnd: "colors";
        borderInlineStart: "colors";
        borderLeft: "colors";
        borderLeftColor: "colors";
        borderRight: "colors";
        borderRightColor: "colors";
        borderTop: "colors";
        borderTopColor: "colors";
        caretColor: "colors";
        color: "colors";
        columnRuleColor: "colors";
        outline: "colors";
        outlineColor: "colors";
        fill: "colors";
        stroke: "colors";
        textDecorationColor: "colors";
        fontFamily: "fonts";
        fontWeight: "fontWeights";
        lineHeight: "lineHeights";
        letterSpacing: "letterSpacings";
        blockSize: "sizes";
        minBlockSize: "sizes";
        maxBlockSize: "sizes";
        inlineSize: "sizes";
        minInlineSize: "sizes";
        maxInlineSize: "sizes";
        width: "sizes";
        minWidth: "sizes";
        maxWidth: "sizes";
        height: "sizes";
        minHeight: "sizes";
        maxHeight: "sizes";
        flexBasis: "sizes";
        gridTemplateColumns: "sizes";
        gridTemplateRows: "sizes";
        borderStyle: "borderStyles";
        borderTopStyle: "borderStyles";
        borderLeftStyle: "borderStyles";
        borderRightStyle: "borderStyles";
        borderBottomStyle: "borderStyles";
        borderRadius: "radii";
        borderTopLeftRadius: "radii";
        borderTopRightRadius: "radii";
        borderBottomRightRadius: "radii";
        borderBottomLeftRadius: "radii";
        boxShadow: "shadows";
        textShadow: "shadows";
        transition: "transitions";
        zIndex: "zIndices";
    }, {
        noOfLines: (value: string | number) => {
            overflow: string;
            display: string;
            "-webkit-box-orient": string;
            "-webkit-line-clamp": string | number;
        };
        w: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
        };
        minW: (value: {
            readonly [$$PropertyValue]: "minWidth";
        }) => {
            minWidth: {
                readonly [$$PropertyValue]: "minWidth";
            };
        };
        maxW: (value: {
            readonly [$$PropertyValue]: "maxWidth";
        }) => {
            maxWidth: {
                readonly [$$PropertyValue]: "maxWidth";
            };
        };
        h: (value: {
            readonly [$$PropertyValue]: "height";
        }) => {
            height: {
                readonly [$$PropertyValue]: "height";
            };
        };
        minH: (value: {
            readonly [$$PropertyValue]: "minHeight";
        }) => {
            minHeight: {
                readonly [$$PropertyValue]: "minHeight";
            };
        };
        maxH: (value: {
            readonly [$$PropertyValue]: "maxHeight";
        }) => {
            maxHeight: {
                readonly [$$PropertyValue]: "maxHeight";
            };
        };
        boxSize: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
            height: {
                readonly [$$PropertyValue]: "width";
            };
        };
        shadow: (value: {
            readonly [$$PropertyValue]: "boxShadow";
        }) => {
            boxShadow: {
                readonly [$$PropertyValue]: "boxShadow";
            };
        };
        p: (value: {
            readonly [$$PropertyValue]: "padding";
        }) => {
            padding: {
                readonly [$$PropertyValue]: "padding";
            };
        };
        pt: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        pr: (value: {
            readonly [$$PropertyValue]: "paddingRight";
        }) => {
            paddingRight: {
                readonly [$$PropertyValue]: "paddingRight";
            };
        };
        paddingStart: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        ps: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        pb: (value: {
            readonly [$$PropertyValue]: "paddingBottom";
        }) => {
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingBottom";
            };
        };
        pl: (value: {
            readonly [$$PropertyValue]: "paddingLeft";
        }) => {
            paddingLeft: {
                readonly [$$PropertyValue]: "paddingLeft";
            };
        };
        pe: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        paddingEnd: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        px: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        py: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        m: (value: {
            readonly [$$PropertyValue]: "margin";
        }) => {
            margin: {
                readonly [$$PropertyValue]: "margin";
            };
        };
        mt: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        mr: (value: {
            readonly [$$PropertyValue]: "marginRight";
        }) => {
            marginRight: {
                readonly [$$PropertyValue]: "marginRight";
            };
        };
        marginStart: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        ms: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        mb: (value: {
            readonly [$$PropertyValue]: "marginBottom";
        }) => {
            marginBottom: {
                readonly [$$PropertyValue]: "marginBottom";
            };
        };
        ml: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
        marginEnd: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        me: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        mx: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        my: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
            marginBottom: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        spaceX: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            "& > * + *": {
                marginLeft: {
                    readonly [$$PropertyValue]: "marginLeft";
                };
            };
        };
        spaceY: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            "& > * + *": {
                marginTop: {
                    readonly [$$PropertyValue]: "marginTop";
                };
            };
        };
        borderTopRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderRightRadius: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        borderStartRadius: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        borderBottomRadius: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        borderLeftRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderEndRadius: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        rounded: (value: {
            readonly [$$PropertyValue]: "borderRadius";
        }) => {
            borderRadius: {
                readonly [$$PropertyValue]: "borderRadius";
            };
        };
        roundedTop: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedRight: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        roundedStart: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        roundedBottom: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        roundedLeft: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedEnd: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        _hover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:hover, &[data-hover]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _active: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:active, &[data-active]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus, &[data-focus]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _highlighted: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-highlighted]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-within": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-visible": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _disabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _readOnly: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _before: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::before": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _after: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::after": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _empty: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:empty": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _expanded: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _checked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _grabbed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _pressed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _invalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _valid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _loading: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _hidden: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _even: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(even)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _odd: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(odd)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _first: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:first-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _last: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:last-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notFirst: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:first-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notLast: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:last-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _visited: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:visited": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeLink: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=page]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeStep: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=step]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _indeterminate: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholder: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::placeholder": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:placeholder-shown": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _fullScreen: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:fullscreen": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selection: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::selection": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaDark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaReduceMotion: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _dark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-dark &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _light: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-light &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        pos: (value: {
            readonly [$$PropertyValue]: "position";
        }) => {
            position: {
                readonly [$$PropertyValue]: "position";
            };
        };
        d: (value: {
            readonly [$$PropertyValue]: "display";
        }) => {
            display: {
                readonly [$$PropertyValue]: "display";
            };
        };
        borderX: (value: {
            readonly [$$PropertyValue]: "borderLeft";
        }) => {
            borderLeft: {
                readonly [$$PropertyValue]: "borderLeft";
            };
            borderRight: {
                readonly [$$PropertyValue]: "borderLeft";
            };
        };
        borderY: (value: {
            readonly [$$PropertyValue]: "borderTop";
        }) => {
            borderTop: {
                readonly [$$PropertyValue]: "borderTop";
            };
            borderBottom: {
                readonly [$$PropertyValue]: "borderTop";
            };
        };
        bg: (value: {
            readonly [$$PropertyValue]: "background";
        }) => {
            background: {
                readonly [$$PropertyValue]: "background";
            };
        };
        bgColor: (value: {
            readonly [$$PropertyValue]: "backgroundColor";
        }) => {
            backgroundColor: {
                readonly [$$PropertyValue]: "backgroundColor";
            };
        };
    }>;
} : K extends `@property ${string}` ? import("@stitches/core/types/css").AtRule.Property : import("@stitches/core/types/css-util").CSS<{
    sm: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
    "reduce-motion": string;
    light: string;
    dark: string;
}, {
    colors: {
        loContrast: string;
        background: string;
        focusRing: string;
        closeButtonHoverBackground: string;
        closeButtonActiveBackground: string;
        progressStripe: string;
        danger1: string;
        danger2: string;
        danger3: string;
        danger4: string;
        danger5: string;
        danger6: string;
        danger7: string;
        danger8: string;
        danger9: string;
        danger10: string;
        danger11: string;
        danger12: string;
        warning1: string;
        warning2: string;
        warning3: string;
        warning4: string;
        warning5: string;
        warning6: string;
        warning7: string;
        warning8: string;
        warning9: string;
        warning10: string;
        warning11: string;
        warning12: string;
        info1: string;
        info2: string;
        info3: string;
        info4: string;
        info5: string;
        info6: string;
        info7: string;
        info8: string;
        info9: string;
        info10: string;
        info11: string;
        info12: string;
        success1: string;
        success2: string;
        success3: string;
        success4: string;
        success5: string;
        success6: string;
        success7: string;
        success8: string;
        success9: string;
        success10: string;
        success11: string;
        success12: string;
        neutral1: string;
        neutral2: string;
        neutral3: string;
        neutral4: string;
        neutral5: string;
        neutral6: string;
        neutral7: string;
        neutral8: string;
        neutral9: string;
        neutral10: string;
        neutral11: string;
        neutral12: string;
        accent1: string;
        accent2: string;
        accent3: string;
        accent4: string;
        accent5: string;
        accent6: string;
        accent7: string;
        accent8: string;
        accent9: string;
        accent10: string;
        accent11: string;
        accent12: string;
        primary1: string;
        primary2: string;
        primary3: string;
        primary4: string;
        primary5: string;
        primary6: string;
        primary7: string;
        primary8: string;
        primary9: string;
        primary10: string;
        primary11: string;
        primary12: string;
        whiteAlpha1: string;
        whiteAlpha2: string;
        whiteAlpha3: string;
        whiteAlpha4: string;
        whiteAlpha5: string;
        whiteAlpha6: string;
        whiteAlpha7: string;
        whiteAlpha8: string;
        whiteAlpha9: string;
        whiteAlpha10: string;
        whiteAlpha11: string;
        whiteAlpha12: string;
        blackAlpha1: string;
        blackAlpha2: string;
        blackAlpha3: string;
        blackAlpha4: string;
        blackAlpha5: string;
        blackAlpha6: string;
        blackAlpha7: string;
        blackAlpha8: string;
        blackAlpha9: string;
        blackAlpha10: string;
        blackAlpha11: string;
        blackAlpha12: string;
    };
    space: {
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    sizes: {
        prose: string;
        max: string;
        min: string;
        full: string;
        screenW: string;
        screenH: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        containerSm: string;
        containerMd: string;
        containerLg: string;
        containerXl: string;
        container2xl: string;
        px: string;
        "0_5": string;
        "1": string;
        "1_5": string;
        "2": string;
        "2_5": string;
        "3": string;
        "3_5": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
        "12": string;
        "14": string;
        "16": string;
        "20": string;
        "24": string;
        "28": string;
        "32": string;
        "36": string;
        "40": string;
        "44": string;
        "48": string;
        "52": string;
        "56": string;
        "60": string;
        "64": string;
        "72": string;
        "80": string;
        "96": string;
    };
    fonts: {
        sans: string;
        serif: string;
        mono: string;
    };
    fontSizes: {
        "2xs": string;
        xs: string;
        sm: string;
        base: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        "4xl": string;
        "5xl": string;
        "6xl": string;
        "7xl": string;
        "8xl": string;
        "9xl": string;
    };
    fontWeights: {
        hairline: number;
        thin: number;
        light: number;
        normal: number;
        medium: number;
        semibold: number;
        bold: number;
        extrabold: number;
        black: number;
    };
    letterSpacings: {
        tighter: string;
        tight: string;
        normal: string;
        wide: string;
        wider: string;
        widest: string;
    };
    lineHeights: {
        normal: string;
        none: number;
        shorter: number;
        short: number;
        base: number;
        tall: number;
        taller: number;
        "3": string;
        "4": string;
        "5": string;
        "6": string;
        "7": string;
        "8": string;
        "9": string;
        "10": string;
    };
    radii: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "3xl": string;
        full: string;
    };
    shadows: {
        none: string;
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        inner: string;
        outline: string;
    };
    zIndices: {
        hide: number;
        auto: string;
        base: number;
        docked: number;
        sticky: number;
        banner: number;
        overlay: number;
        modal: number;
        dropdown: number;
        popover: number;
        tooltip: number;
        skipLink: number;
        notification: number;
    };
}, {
    borderWidth: "sizes";
    borderTopWidth: "sizes";
    borderRightWidth: "sizes";
    borderBottomWidth: "sizes";
    borderLeftWidth: "sizes";
    strokeWidth: "sizes";
    gap: "space";
    gridGap: "space";
    columnGap: "space";
    gridColumnGap: "space";
    rowGap: "space";
    gridRowGap: "space";
    inset: "space";
    insetBlock: "space";
    insetBlockEnd: "space";
    insetBlockStart: "space";
    insetInline: "space";
    insetInlineEnd: "space";
    insetInlineStart: "space";
    margin: "space";
    marginTop: "space";
    marginRight: "space";
    marginBottom: "space";
    marginLeft: "space";
    marginBlock: "space";
    marginBlockEnd: "space";
    marginBlockStart: "space";
    marginInline: "space";
    marginInlineEnd: "space";
    marginInlineStart: "space";
    padding: "space";
    paddingTop: "space";
    paddingRight: "space";
    paddingBottom: "space";
    paddingLeft: "space";
    paddingBlock: "space";
    paddingBlockEnd: "space";
    paddingBlockStart: "space";
    paddingInline: "space";
    paddingInlineEnd: "space";
    paddingInlineStart: "space";
    scrollMargin: "space";
    scrollMarginTop: "space";
    scrollMarginRight: "space";
    scrollMarginBottom: "space";
    scrollMarginLeft: "space";
    scrollMarginBlock: "space";
    scrollMarginBlockEnd: "space";
    scrollMarginBlockStart: "space";
    scrollMarginInline: "space";
    scrollMarginInlineEnd: "space";
    scrollMarginInlineStart: "space";
    scrollPadding: "space";
    scrollPaddingTop: "space";
    scrollPaddingRight: "space";
    scrollPaddingBottom: "space";
    scrollPaddingLeft: "space";
    scrollPaddingBlock: "space";
    scrollPaddingBlockEnd: "space";
    scrollPaddingBlockStart: "space";
    scrollPaddingInline: "space";
    scrollPaddingInlineEnd: "space";
    scrollPaddingInlineStart: "space";
    top: "space";
    right: "space";
    bottom: "space";
    left: "space";
    fontSize: "fontSizes";
    background: "colors";
    backgroundColor: "colors";
    backgroundImage: "colors";
    borderImage: "colors";
    border: "colors";
    borderBlock: "colors";
    borderBlockEnd: "colors";
    borderBlockStart: "colors";
    borderBottom: "colors";
    borderBottomColor: "colors";
    borderColor: "colors";
    borderInline: "colors";
    borderInlineEnd: "colors";
    borderInlineStart: "colors";
    borderLeft: "colors";
    borderLeftColor: "colors";
    borderRight: "colors";
    borderRightColor: "colors";
    borderTop: "colors";
    borderTopColor: "colors";
    caretColor: "colors";
    color: "colors";
    columnRuleColor: "colors";
    outline: "colors";
    outlineColor: "colors";
    fill: "colors";
    stroke: "colors";
    textDecorationColor: "colors";
    fontFamily: "fonts";
    fontWeight: "fontWeights";
    lineHeight: "lineHeights";
    letterSpacing: "letterSpacings";
    blockSize: "sizes";
    minBlockSize: "sizes";
    maxBlockSize: "sizes";
    inlineSize: "sizes";
    minInlineSize: "sizes";
    maxInlineSize: "sizes";
    width: "sizes";
    minWidth: "sizes";
    maxWidth: "sizes";
    height: "sizes";
    minHeight: "sizes";
    maxHeight: "sizes";
    flexBasis: "sizes";
    gridTemplateColumns: "sizes";
    gridTemplateRows: "sizes";
    borderStyle: "borderStyles";
    borderTopStyle: "borderStyles";
    borderLeftStyle: "borderStyles";
    borderRightStyle: "borderStyles";
    borderBottomStyle: "borderStyles";
    borderRadius: "radii";
    borderTopLeftRadius: "radii";
    borderTopRightRadius: "radii";
    borderBottomRightRadius: "radii";
    borderBottomLeftRadius: "radii";
    boxShadow: "shadows";
    textShadow: "shadows";
    transition: "transitions";
    zIndex: "zIndices";
}, {
    noOfLines: (value: string | number) => {
        overflow: string;
        display: string;
        "-webkit-box-orient": string;
        "-webkit-line-clamp": string | number;
    };
    w: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
    };
    minW: (value: {
        readonly [$$PropertyValue]: "minWidth";
    }) => {
        minWidth: {
            readonly [$$PropertyValue]: "minWidth";
        };
    };
    maxW: (value: {
        readonly [$$PropertyValue]: "maxWidth";
    }) => {
        maxWidth: {
            readonly [$$PropertyValue]: "maxWidth";
        };
    };
    h: (value: {
        readonly [$$PropertyValue]: "height";
    }) => {
        height: {
            readonly [$$PropertyValue]: "height";
        };
    };
    minH: (value: {
        readonly [$$PropertyValue]: "minHeight";
    }) => {
        minHeight: {
            readonly [$$PropertyValue]: "minHeight";
        };
    };
    maxH: (value: {
        readonly [$$PropertyValue]: "maxHeight";
    }) => {
        maxHeight: {
            readonly [$$PropertyValue]: "maxHeight";
        };
    };
    boxSize: (value: {
        readonly [$$PropertyValue]: "width";
    }) => {
        width: {
            readonly [$$PropertyValue]: "width";
        };
        height: {
            readonly [$$PropertyValue]: "width";
        };
    };
    shadow: (value: {
        readonly [$$PropertyValue]: "boxShadow";
    }) => {
        boxShadow: {
            readonly [$$PropertyValue]: "boxShadow";
        };
    };
    p: (value: {
        readonly [$$PropertyValue]: "padding";
    }) => {
        padding: {
            readonly [$$PropertyValue]: "padding";
        };
    };
    pt: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    pr: (value: {
        readonly [$$PropertyValue]: "paddingRight";
    }) => {
        paddingRight: {
            readonly [$$PropertyValue]: "paddingRight";
        };
    };
    paddingStart: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    ps: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    pb: (value: {
        readonly [$$PropertyValue]: "paddingBottom";
    }) => {
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingBottom";
        };
    };
    pl: (value: {
        readonly [$$PropertyValue]: "paddingLeft";
    }) => {
        paddingLeft: {
            readonly [$$PropertyValue]: "paddingLeft";
        };
    };
    pe: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    paddingEnd: (value: {
        readonly [$$PropertyValue]: "paddingInlineEnd";
    }) => {
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        };
    };
    px: (value: {
        readonly [$$PropertyValue]: "paddingInlineStart";
    }) => {
        paddingInlineStart: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
        paddingInlineEnd: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        };
    };
    py: (value: {
        readonly [$$PropertyValue]: "paddingTop";
    }) => {
        paddingTop: {
            readonly [$$PropertyValue]: "paddingTop";
        };
        paddingBottom: {
            readonly [$$PropertyValue]: "paddingTop";
        };
    };
    m: (value: {
        readonly [$$PropertyValue]: "margin";
    }) => {
        margin: {
            readonly [$$PropertyValue]: "margin";
        };
    };
    mt: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    mr: (value: {
        readonly [$$PropertyValue]: "marginRight";
    }) => {
        marginRight: {
            readonly [$$PropertyValue]: "marginRight";
        };
    };
    marginStart: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    ms: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    mb: (value: {
        readonly [$$PropertyValue]: "marginBottom";
    }) => {
        marginBottom: {
            readonly [$$PropertyValue]: "marginBottom";
        };
    };
    ml: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        marginLeft: {
            readonly [$$PropertyValue]: "marginLeft";
        };
    };
    marginEnd: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    me: (value: {
        readonly [$$PropertyValue]: "marginInlineEnd";
    }) => {
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        };
    };
    mx: (value: {
        readonly [$$PropertyValue]: "marginInlineStart";
    }) => {
        marginInlineStart: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
        marginInlineEnd: {
            readonly [$$PropertyValue]: "marginInlineStart";
        };
    };
    my: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        marginTop: {
            readonly [$$PropertyValue]: "marginTop";
        };
        marginBottom: {
            readonly [$$PropertyValue]: "marginTop";
        };
    };
    spaceX: (value: {
        readonly [$$PropertyValue]: "marginLeft";
    }) => {
        "& > * + *": {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
    };
    spaceY: (value: {
        readonly [$$PropertyValue]: "marginTop";
    }) => {
        "& > * + *": {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
    };
    borderTopRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderRightRadius: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    borderStartRadius: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    borderBottomRadius: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    borderLeftRadius: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    borderEndRadius: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    rounded: (value: {
        readonly [$$PropertyValue]: "borderRadius";
    }) => {
        borderRadius: {
            readonly [$$PropertyValue]: "borderRadius";
        };
    };
    roundedTop: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedRight: (value: {
        readonly [$$PropertyValue]: "borderTopRightRadius";
    }) => {
        borderTopRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        };
    };
    roundedStart: (value: {
        readonly [$$PropertyValue]: "borderStartStartRadius";
    }) => {
        borderStartStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
        borderEndStartRadius: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        };
    };
    roundedBottom: (value: {
        readonly [$$PropertyValue]: "borderBottomLeftRadius";
    }) => {
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
        borderBottomRightRadius: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        };
    };
    roundedLeft: (value: {
        readonly [$$PropertyValue]: "borderTopLeftRadius";
    }) => {
        borderTopLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
        borderBottomLeftRadius: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        };
    };
    roundedEnd: (value: {
        readonly [$$PropertyValue]: "borderStartEndRadius";
    }) => {
        borderStartEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
        borderEndEndRadius: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        };
    };
    _hover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:hover, &[data-hover]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _active: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:active, &[data-active]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus, &[data-focus]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _highlighted: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-highlighted]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-within": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _focusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:focus-visible": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _disabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _readOnly: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _before: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::before": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _after: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::after": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _empty: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:empty": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _expanded: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _checked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _grabbed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _pressed: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _invalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _valid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _loading: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _hidden: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _even: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(even)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _odd: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:nth-of-type(odd)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _first: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:first-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _last: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:last-of-type": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notFirst: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:first-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _notLast: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:not(:last-of-type)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _visited: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:visited": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeLink: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=page]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _activeStep: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&[aria-current=step]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _indeterminate: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerHover: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocus: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusVisible: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerActive: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerSelected: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerDisabled: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerInvalid: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerChecked: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _groupFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerFocusWithin: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        [x: string]: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholder: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::placeholder": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _placeholderShown: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:placeholder-shown": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _fullScreen: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&:fullscreen": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _selection: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "&::selection": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaDark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _mediaReduceMotion: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _dark: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-dark &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    _light: (value: import("@stitches/core").CSS<{
        media: {};
        theme: {};
        themeMap: {};
        utils: {};
    }>) => {
        ".hope-ui-light &": import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>;
    };
    pos: (value: {
        readonly [$$PropertyValue]: "position";
    }) => {
        position: {
            readonly [$$PropertyValue]: "position";
        };
    };
    d: (value: {
        readonly [$$PropertyValue]: "display";
    }) => {
        display: {
            readonly [$$PropertyValue]: "display";
        };
    };
    borderX: (value: {
        readonly [$$PropertyValue]: "borderLeft";
    }) => {
        borderLeft: {
            readonly [$$PropertyValue]: "borderLeft";
        };
        borderRight: {
            readonly [$$PropertyValue]: "borderLeft";
        };
    };
    borderY: (value: {
        readonly [$$PropertyValue]: "borderTop";
    }) => {
        borderTop: {
            readonly [$$PropertyValue]: "borderTop";
        };
        borderBottom: {
            readonly [$$PropertyValue]: "borderTop";
        };
    };
    bg: (value: {
        readonly [$$PropertyValue]: "background";
    }) => {
        background: {
            readonly [$$PropertyValue]: "background";
        };
    };
    bgColor: (value: {
        readonly [$$PropertyValue]: "backgroundColor";
    }) => {
        backgroundColor: {
            readonly [$$PropertyValue]: "backgroundColor";
        };
    };
}>; })[]) => () => string, config: {
    prefix: "hope";
    media: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "reduce-motion": string;
        light: string;
        dark: string;
    };
    theme: {
        colors: {
            loContrast: string;
            background: string;
            focusRing: string;
            closeButtonHoverBackground: string;
            closeButtonActiveBackground: string;
            progressStripe: string;
            danger1: string;
            danger2: string;
            danger3: string;
            danger4: string;
            danger5: string;
            danger6: string;
            danger7: string;
            danger8: string;
            danger9: string;
            danger10: string;
            danger11: string;
            danger12: string;
            warning1: string;
            warning2: string;
            warning3: string;
            warning4: string;
            warning5: string;
            warning6: string;
            warning7: string;
            warning8: string;
            warning9: string;
            warning10: string;
            warning11: string;
            warning12: string;
            info1: string;
            info2: string;
            info3: string;
            info4: string;
            info5: string;
            info6: string;
            info7: string;
            info8: string;
            info9: string;
            info10: string;
            info11: string;
            info12: string;
            success1: string;
            success2: string;
            success3: string;
            success4: string;
            success5: string;
            success6: string;
            success7: string;
            success8: string;
            success9: string;
            success10: string;
            success11: string;
            success12: string;
            neutral1: string;
            neutral2: string;
            neutral3: string;
            neutral4: string;
            neutral5: string;
            neutral6: string;
            neutral7: string;
            neutral8: string;
            neutral9: string;
            neutral10: string;
            neutral11: string;
            neutral12: string;
            accent1: string;
            accent2: string;
            accent3: string;
            accent4: string;
            accent5: string;
            accent6: string;
            accent7: string;
            accent8: string;
            accent9: string;
            accent10: string;
            accent11: string;
            accent12: string;
            primary1: string;
            primary2: string;
            primary3: string;
            primary4: string;
            primary5: string;
            primary6: string;
            primary7: string;
            primary8: string;
            primary9: string;
            primary10: string;
            primary11: string;
            primary12: string;
            whiteAlpha1: string;
            whiteAlpha2: string;
            whiteAlpha3: string;
            whiteAlpha4: string;
            whiteAlpha5: string;
            whiteAlpha6: string;
            whiteAlpha7: string;
            whiteAlpha8: string;
            whiteAlpha9: string;
            whiteAlpha10: string;
            whiteAlpha11: string;
            whiteAlpha12: string;
            blackAlpha1: string;
            blackAlpha2: string;
            blackAlpha3: string;
            blackAlpha4: string;
            blackAlpha5: string;
            blackAlpha6: string;
            blackAlpha7: string;
            blackAlpha8: string;
            blackAlpha9: string;
            blackAlpha10: string;
            blackAlpha11: string;
            blackAlpha12: string;
        };
        space: {
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        sizes: {
            prose: string;
            max: string;
            min: string;
            full: string;
            screenW: string;
            screenH: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            containerSm: string;
            containerMd: string;
            containerLg: string;
            containerXl: string;
            container2xl: string;
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        fonts: {
            sans: string;
            serif: string;
            mono: string;
        };
        fontSizes: {
            "2xs": string;
            xs: string;
            sm: string;
            base: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            "9xl": string;
        };
        fontWeights: {
            hairline: number;
            thin: number;
            light: number;
            normal: number;
            medium: number;
            semibold: number;
            bold: number;
            extrabold: number;
            black: number;
        };
        letterSpacings: {
            tighter: string;
            tight: string;
            normal: string;
            wide: string;
            wider: string;
            widest: string;
        };
        lineHeights: {
            normal: string;
            none: number;
            shorter: number;
            short: number;
            base: number;
            tall: number;
            taller: number;
            "3": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
        };
        radii: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            full: string;
        };
        shadows: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            inner: string;
            outline: string;
        };
        zIndices: {
            hide: number;
            auto: string;
            base: number;
            docked: number;
            sticky: number;
            banner: number;
            overlay: number;
            modal: number;
            dropdown: number;
            popover: number;
            tooltip: number;
            skipLink: number;
            notification: number;
        };
    };
    themeMap: {
        borderWidth: "sizes";
        borderTopWidth: "sizes";
        borderRightWidth: "sizes";
        borderBottomWidth: "sizes";
        borderLeftWidth: "sizes";
        strokeWidth: "sizes";
        gap: "space";
        gridGap: "space";
        columnGap: "space";
        gridColumnGap: "space";
        rowGap: "space";
        gridRowGap: "space";
        inset: "space";
        insetBlock: "space";
        insetBlockEnd: "space";
        insetBlockStart: "space";
        insetInline: "space";
        insetInlineEnd: "space";
        insetInlineStart: "space";
        margin: "space";
        marginTop: "space";
        marginRight: "space";
        marginBottom: "space";
        marginLeft: "space";
        marginBlock: "space";
        marginBlockEnd: "space";
        marginBlockStart: "space";
        marginInline: "space";
        marginInlineEnd: "space";
        marginInlineStart: "space";
        padding: "space";
        paddingTop: "space";
        paddingRight: "space";
        paddingBottom: "space";
        paddingLeft: "space";
        paddingBlock: "space";
        paddingBlockEnd: "space";
        paddingBlockStart: "space";
        paddingInline: "space";
        paddingInlineEnd: "space";
        paddingInlineStart: "space";
        scrollMargin: "space";
        scrollMarginTop: "space";
        scrollMarginRight: "space";
        scrollMarginBottom: "space";
        scrollMarginLeft: "space";
        scrollMarginBlock: "space";
        scrollMarginBlockEnd: "space";
        scrollMarginBlockStart: "space";
        scrollMarginInline: "space";
        scrollMarginInlineEnd: "space";
        scrollMarginInlineStart: "space";
        scrollPadding: "space";
        scrollPaddingTop: "space";
        scrollPaddingRight: "space";
        scrollPaddingBottom: "space";
        scrollPaddingLeft: "space";
        scrollPaddingBlock: "space";
        scrollPaddingBlockEnd: "space";
        scrollPaddingBlockStart: "space";
        scrollPaddingInline: "space";
        scrollPaddingInlineEnd: "space";
        scrollPaddingInlineStart: "space";
        top: "space";
        right: "space";
        bottom: "space";
        left: "space";
        fontSize: "fontSizes";
        background: "colors";
        backgroundColor: "colors";
        backgroundImage: "colors";
        borderImage: "colors";
        border: "colors";
        borderBlock: "colors";
        borderBlockEnd: "colors";
        borderBlockStart: "colors";
        borderBottom: "colors";
        borderBottomColor: "colors";
        borderColor: "colors";
        borderInline: "colors";
        borderInlineEnd: "colors";
        borderInlineStart: "colors";
        borderLeft: "colors";
        borderLeftColor: "colors";
        borderRight: "colors";
        borderRightColor: "colors";
        borderTop: "colors";
        borderTopColor: "colors";
        caretColor: "colors";
        color: "colors";
        columnRuleColor: "colors";
        outline: "colors";
        outlineColor: "colors";
        fill: "colors";
        stroke: "colors";
        textDecorationColor: "colors";
        fontFamily: "fonts";
        fontWeight: "fontWeights";
        lineHeight: "lineHeights";
        letterSpacing: "letterSpacings";
        blockSize: "sizes";
        minBlockSize: "sizes";
        maxBlockSize: "sizes";
        inlineSize: "sizes";
        minInlineSize: "sizes";
        maxInlineSize: "sizes";
        width: "sizes";
        minWidth: "sizes";
        maxWidth: "sizes";
        height: "sizes";
        minHeight: "sizes";
        maxHeight: "sizes";
        flexBasis: "sizes";
        gridTemplateColumns: "sizes";
        gridTemplateRows: "sizes";
        borderStyle: "borderStyles";
        borderTopStyle: "borderStyles";
        borderLeftStyle: "borderStyles";
        borderRightStyle: "borderStyles";
        borderBottomStyle: "borderStyles";
        borderRadius: "radii";
        borderTopLeftRadius: "radii";
        borderTopRightRadius: "radii";
        borderBottomRightRadius: "radii";
        borderBottomLeftRadius: "radii";
        boxShadow: "shadows";
        textShadow: "shadows";
        transition: "transitions";
        zIndex: "zIndices";
    };
    utils: {
        noOfLines: (value: string | number) => {
            overflow: string;
            display: string;
            "-webkit-box-orient": string;
            "-webkit-line-clamp": string | number;
        };
        w: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
        };
        minW: (value: {
            readonly [$$PropertyValue]: "minWidth";
        }) => {
            minWidth: {
                readonly [$$PropertyValue]: "minWidth";
            };
        };
        maxW: (value: {
            readonly [$$PropertyValue]: "maxWidth";
        }) => {
            maxWidth: {
                readonly [$$PropertyValue]: "maxWidth";
            };
        };
        h: (value: {
            readonly [$$PropertyValue]: "height";
        }) => {
            height: {
                readonly [$$PropertyValue]: "height";
            };
        };
        minH: (value: {
            readonly [$$PropertyValue]: "minHeight";
        }) => {
            minHeight: {
                readonly [$$PropertyValue]: "minHeight";
            };
        };
        maxH: (value: {
            readonly [$$PropertyValue]: "maxHeight";
        }) => {
            maxHeight: {
                readonly [$$PropertyValue]: "maxHeight";
            };
        };
        boxSize: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
            height: {
                readonly [$$PropertyValue]: "width";
            };
        };
        shadow: (value: {
            readonly [$$PropertyValue]: "boxShadow";
        }) => {
            boxShadow: {
                readonly [$$PropertyValue]: "boxShadow";
            };
        };
        p: (value: {
            readonly [$$PropertyValue]: "padding";
        }) => {
            padding: {
                readonly [$$PropertyValue]: "padding";
            };
        };
        pt: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        pr: (value: {
            readonly [$$PropertyValue]: "paddingRight";
        }) => {
            paddingRight: {
                readonly [$$PropertyValue]: "paddingRight";
            };
        };
        paddingStart: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        ps: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        pb: (value: {
            readonly [$$PropertyValue]: "paddingBottom";
        }) => {
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingBottom";
            };
        };
        pl: (value: {
            readonly [$$PropertyValue]: "paddingLeft";
        }) => {
            paddingLeft: {
                readonly [$$PropertyValue]: "paddingLeft";
            };
        };
        pe: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        paddingEnd: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        px: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        py: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        m: (value: {
            readonly [$$PropertyValue]: "margin";
        }) => {
            margin: {
                readonly [$$PropertyValue]: "margin";
            };
        };
        mt: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        mr: (value: {
            readonly [$$PropertyValue]: "marginRight";
        }) => {
            marginRight: {
                readonly [$$PropertyValue]: "marginRight";
            };
        };
        marginStart: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        ms: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        mb: (value: {
            readonly [$$PropertyValue]: "marginBottom";
        }) => {
            marginBottom: {
                readonly [$$PropertyValue]: "marginBottom";
            };
        };
        ml: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
        marginEnd: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        me: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        mx: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        my: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
            marginBottom: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        spaceX: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            "& > * + *": {
                marginLeft: {
                    readonly [$$PropertyValue]: "marginLeft";
                };
            };
        };
        spaceY: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            "& > * + *": {
                marginTop: {
                    readonly [$$PropertyValue]: "marginTop";
                };
            };
        };
        borderTopRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderRightRadius: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        borderStartRadius: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        borderBottomRadius: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        borderLeftRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderEndRadius: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        rounded: (value: {
            readonly [$$PropertyValue]: "borderRadius";
        }) => {
            borderRadius: {
                readonly [$$PropertyValue]: "borderRadius";
            };
        };
        roundedTop: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedRight: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        roundedStart: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        roundedBottom: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        roundedLeft: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedEnd: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        _hover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:hover, &[data-hover]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _active: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:active, &[data-active]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus, &[data-focus]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _highlighted: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-highlighted]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-within": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-visible": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _disabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _readOnly: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _before: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::before": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _after: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::after": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _empty: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:empty": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _expanded: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _checked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _grabbed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _pressed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _invalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _valid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _loading: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _hidden: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _even: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(even)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _odd: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(odd)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _first: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:first-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _last: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:last-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notFirst: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:first-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notLast: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:last-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _visited: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:visited": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeLink: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=page]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeStep: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=step]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _indeterminate: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholder: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::placeholder": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:placeholder-shown": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _fullScreen: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:fullscreen": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selection: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::selection": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaDark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaReduceMotion: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _dark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-dark &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _light: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-light &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        pos: (value: {
            readonly [$$PropertyValue]: "position";
        }) => {
            position: {
                readonly [$$PropertyValue]: "position";
            };
        };
        d: (value: {
            readonly [$$PropertyValue]: "display";
        }) => {
            display: {
                readonly [$$PropertyValue]: "display";
            };
        };
        borderX: (value: {
            readonly [$$PropertyValue]: "borderLeft";
        }) => {
            borderLeft: {
                readonly [$$PropertyValue]: "borderLeft";
            };
            borderRight: {
                readonly [$$PropertyValue]: "borderLeft";
            };
        };
        borderY: (value: {
            readonly [$$PropertyValue]: "borderTop";
        }) => {
            borderTop: {
                readonly [$$PropertyValue]: "borderTop";
            };
            borderBottom: {
                readonly [$$PropertyValue]: "borderTop";
            };
        };
        bg: (value: {
            readonly [$$PropertyValue]: "background";
        }) => {
            background: {
                readonly [$$PropertyValue]: "background";
            };
        };
        bgColor: (value: {
            readonly [$$PropertyValue]: "backgroundColor";
        }) => {
            backgroundColor: {
                readonly [$$PropertyValue]: "backgroundColor";
            };
        };
    };
}, createTheme: <Argument0 extends string | ({
    colors?: {
        loContrast?: string | number | boolean | undefined;
        background?: string | number | boolean | undefined;
        focusRing?: string | number | boolean | undefined;
        closeButtonHoverBackground?: string | number | boolean | undefined;
        closeButtonActiveBackground?: string | number | boolean | undefined;
        progressStripe?: string | number | boolean | undefined;
        danger1?: string | number | boolean | undefined;
        danger2?: string | number | boolean | undefined;
        danger3?: string | number | boolean | undefined;
        danger4?: string | number | boolean | undefined;
        danger5?: string | number | boolean | undefined;
        danger6?: string | number | boolean | undefined;
        danger7?: string | number | boolean | undefined;
        danger8?: string | number | boolean | undefined;
        danger9?: string | number | boolean | undefined;
        danger10?: string | number | boolean | undefined;
        danger11?: string | number | boolean | undefined;
        danger12?: string | number | boolean | undefined;
        warning1?: string | number | boolean | undefined;
        warning2?: string | number | boolean | undefined;
        warning3?: string | number | boolean | undefined;
        warning4?: string | number | boolean | undefined;
        warning5?: string | number | boolean | undefined;
        warning6?: string | number | boolean | undefined;
        warning7?: string | number | boolean | undefined;
        warning8?: string | number | boolean | undefined;
        warning9?: string | number | boolean | undefined;
        warning10?: string | number | boolean | undefined;
        warning11?: string | number | boolean | undefined;
        warning12?: string | number | boolean | undefined;
        info1?: string | number | boolean | undefined;
        info2?: string | number | boolean | undefined;
        info3?: string | number | boolean | undefined;
        info4?: string | number | boolean | undefined;
        info5?: string | number | boolean | undefined;
        info6?: string | number | boolean | undefined;
        info7?: string | number | boolean | undefined;
        info8?: string | number | boolean | undefined;
        info9?: string | number | boolean | undefined;
        info10?: string | number | boolean | undefined;
        info11?: string | number | boolean | undefined;
        info12?: string | number | boolean | undefined;
        success1?: string | number | boolean | undefined;
        success2?: string | number | boolean | undefined;
        success3?: string | number | boolean | undefined;
        success4?: string | number | boolean | undefined;
        success5?: string | number | boolean | undefined;
        success6?: string | number | boolean | undefined;
        success7?: string | number | boolean | undefined;
        success8?: string | number | boolean | undefined;
        success9?: string | number | boolean | undefined;
        success10?: string | number | boolean | undefined;
        success11?: string | number | boolean | undefined;
        success12?: string | number | boolean | undefined;
        neutral1?: string | number | boolean | undefined;
        neutral2?: string | number | boolean | undefined;
        neutral3?: string | number | boolean | undefined;
        neutral4?: string | number | boolean | undefined;
        neutral5?: string | number | boolean | undefined;
        neutral6?: string | number | boolean | undefined;
        neutral7?: string | number | boolean | undefined;
        neutral8?: string | number | boolean | undefined;
        neutral9?: string | number | boolean | undefined;
        neutral10?: string | number | boolean | undefined;
        neutral11?: string | number | boolean | undefined;
        neutral12?: string | number | boolean | undefined;
        accent1?: string | number | boolean | undefined;
        accent2?: string | number | boolean | undefined;
        accent3?: string | number | boolean | undefined;
        accent4?: string | number | boolean | undefined;
        accent5?: string | number | boolean | undefined;
        accent6?: string | number | boolean | undefined;
        accent7?: string | number | boolean | undefined;
        accent8?: string | number | boolean | undefined;
        accent9?: string | number | boolean | undefined;
        accent10?: string | number | boolean | undefined;
        accent11?: string | number | boolean | undefined;
        accent12?: string | number | boolean | undefined;
        primary1?: string | number | boolean | undefined;
        primary2?: string | number | boolean | undefined;
        primary3?: string | number | boolean | undefined;
        primary4?: string | number | boolean | undefined;
        primary5?: string | number | boolean | undefined;
        primary6?: string | number | boolean | undefined;
        primary7?: string | number | boolean | undefined;
        primary8?: string | number | boolean | undefined;
        primary9?: string | number | boolean | undefined;
        primary10?: string | number | boolean | undefined;
        primary11?: string | number | boolean | undefined;
        primary12?: string | number | boolean | undefined;
        whiteAlpha1?: string | number | boolean | undefined;
        whiteAlpha2?: string | number | boolean | undefined;
        whiteAlpha3?: string | number | boolean | undefined;
        whiteAlpha4?: string | number | boolean | undefined;
        whiteAlpha5?: string | number | boolean | undefined;
        whiteAlpha6?: string | number | boolean | undefined;
        whiteAlpha7?: string | number | boolean | undefined;
        whiteAlpha8?: string | number | boolean | undefined;
        whiteAlpha9?: string | number | boolean | undefined;
        whiteAlpha10?: string | number | boolean | undefined;
        whiteAlpha11?: string | number | boolean | undefined;
        whiteAlpha12?: string | number | boolean | undefined;
        blackAlpha1?: string | number | boolean | undefined;
        blackAlpha2?: string | number | boolean | undefined;
        blackAlpha3?: string | number | boolean | undefined;
        blackAlpha4?: string | number | boolean | undefined;
        blackAlpha5?: string | number | boolean | undefined;
        blackAlpha6?: string | number | boolean | undefined;
        blackAlpha7?: string | number | boolean | undefined;
        blackAlpha8?: string | number | boolean | undefined;
        blackAlpha9?: string | number | boolean | undefined;
        blackAlpha10?: string | number | boolean | undefined;
        blackAlpha11?: string | number | boolean | undefined;
        blackAlpha12?: string | number | boolean | undefined;
    } | undefined;
    space?: {
        px?: string | number | boolean | undefined;
        "0_5"?: string | number | boolean | undefined;
        1?: string | number | boolean | undefined;
        "1_5"?: string | number | boolean | undefined;
        2?: string | number | boolean | undefined;
        "2_5"?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        "3_5"?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
        12?: string | number | boolean | undefined;
        14?: string | number | boolean | undefined;
        16?: string | number | boolean | undefined;
        20?: string | number | boolean | undefined;
        24?: string | number | boolean | undefined;
        28?: string | number | boolean | undefined;
        32?: string | number | boolean | undefined;
        36?: string | number | boolean | undefined;
        40?: string | number | boolean | undefined;
        44?: string | number | boolean | undefined;
        48?: string | number | boolean | undefined;
        52?: string | number | boolean | undefined;
        56?: string | number | boolean | undefined;
        60?: string | number | boolean | undefined;
        64?: string | number | boolean | undefined;
        72?: string | number | boolean | undefined;
        80?: string | number | boolean | undefined;
        96?: string | number | boolean | undefined;
    } | undefined;
    sizes?: {
        prose?: string | number | boolean | undefined;
        max?: string | number | boolean | undefined;
        min?: string | number | boolean | undefined;
        full?: string | number | boolean | undefined;
        screenW?: string | number | boolean | undefined;
        screenH?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        "4xl"?: string | number | boolean | undefined;
        "5xl"?: string | number | boolean | undefined;
        "6xl"?: string | number | boolean | undefined;
        "7xl"?: string | number | boolean | undefined;
        "8xl"?: string | number | boolean | undefined;
        containerSm?: string | number | boolean | undefined;
        containerMd?: string | number | boolean | undefined;
        containerLg?: string | number | boolean | undefined;
        containerXl?: string | number | boolean | undefined;
        container2xl?: string | number | boolean | undefined;
        px?: string | number | boolean | undefined;
        "0_5"?: string | number | boolean | undefined;
        1?: string | number | boolean | undefined;
        "1_5"?: string | number | boolean | undefined;
        2?: string | number | boolean | undefined;
        "2_5"?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        "3_5"?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
        12?: string | number | boolean | undefined;
        14?: string | number | boolean | undefined;
        16?: string | number | boolean | undefined;
        20?: string | number | boolean | undefined;
        24?: string | number | boolean | undefined;
        28?: string | number | boolean | undefined;
        32?: string | number | boolean | undefined;
        36?: string | number | boolean | undefined;
        40?: string | number | boolean | undefined;
        44?: string | number | boolean | undefined;
        48?: string | number | boolean | undefined;
        52?: string | number | boolean | undefined;
        56?: string | number | boolean | undefined;
        60?: string | number | boolean | undefined;
        64?: string | number | boolean | undefined;
        72?: string | number | boolean | undefined;
        80?: string | number | boolean | undefined;
        96?: string | number | boolean | undefined;
    } | undefined;
    fonts?: {
        sans?: string | number | boolean | undefined;
        serif?: string | number | boolean | undefined;
        mono?: string | number | boolean | undefined;
    } | undefined;
    fontSizes?: {
        "2xs"?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        "4xl"?: string | number | boolean | undefined;
        "5xl"?: string | number | boolean | undefined;
        "6xl"?: string | number | boolean | undefined;
        "7xl"?: string | number | boolean | undefined;
        "8xl"?: string | number | boolean | undefined;
        "9xl"?: string | number | boolean | undefined;
    } | undefined;
    fontWeights?: {
        hairline?: string | number | boolean | undefined;
        thin?: string | number | boolean | undefined;
        light?: string | number | boolean | undefined;
        normal?: string | number | boolean | undefined;
        medium?: string | number | boolean | undefined;
        semibold?: string | number | boolean | undefined;
        bold?: string | number | boolean | undefined;
        extrabold?: string | number | boolean | undefined;
        black?: string | number | boolean | undefined;
    } | undefined;
    letterSpacings?: {
        tighter?: string | number | boolean | undefined;
        tight?: string | number | boolean | undefined;
        normal?: string | number | boolean | undefined;
        wide?: string | number | boolean | undefined;
        wider?: string | number | boolean | undefined;
        widest?: string | number | boolean | undefined;
    } | undefined;
    lineHeights?: {
        normal?: string | number | boolean | undefined;
        none?: string | number | boolean | undefined;
        shorter?: string | number | boolean | undefined;
        short?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        tall?: string | number | boolean | undefined;
        taller?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
    } | undefined;
    radii?: {
        none?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        full?: string | number | boolean | undefined;
    } | undefined;
    shadows?: {
        none?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        inner?: string | number | boolean | undefined;
        outline?: string | number | boolean | undefined;
    } | undefined;
    zIndices?: {
        hide?: string | number | boolean | undefined;
        auto?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        docked?: string | number | boolean | undefined;
        sticky?: string | number | boolean | undefined;
        banner?: string | number | boolean | undefined;
        overlay?: string | number | boolean | undefined;
        modal?: string | number | boolean | undefined;
        dropdown?: string | number | boolean | undefined;
        popover?: string | number | boolean | undefined;
        tooltip?: string | number | boolean | undefined;
        skipLink?: string | number | boolean | undefined;
        notification?: string | number | boolean | undefined;
    } | undefined;
} & {
    [x: string]: {
        [x: string]: string | number | boolean;
        [x: number]: string | number | boolean;
    };
}), Argument1 extends string | ({
    colors?: {
        loContrast?: string | number | boolean | undefined;
        background?: string | number | boolean | undefined;
        focusRing?: string | number | boolean | undefined;
        closeButtonHoverBackground?: string | number | boolean | undefined;
        closeButtonActiveBackground?: string | number | boolean | undefined;
        progressStripe?: string | number | boolean | undefined;
        danger1?: string | number | boolean | undefined;
        danger2?: string | number | boolean | undefined;
        danger3?: string | number | boolean | undefined;
        danger4?: string | number | boolean | undefined;
        danger5?: string | number | boolean | undefined;
        danger6?: string | number | boolean | undefined;
        danger7?: string | number | boolean | undefined;
        danger8?: string | number | boolean | undefined;
        danger9?: string | number | boolean | undefined;
        danger10?: string | number | boolean | undefined;
        danger11?: string | number | boolean | undefined;
        danger12?: string | number | boolean | undefined;
        warning1?: string | number | boolean | undefined;
        warning2?: string | number | boolean | undefined;
        warning3?: string | number | boolean | undefined;
        warning4?: string | number | boolean | undefined;
        warning5?: string | number | boolean | undefined;
        warning6?: string | number | boolean | undefined;
        warning7?: string | number | boolean | undefined;
        warning8?: string | number | boolean | undefined;
        warning9?: string | number | boolean | undefined;
        warning10?: string | number | boolean | undefined;
        warning11?: string | number | boolean | undefined;
        warning12?: string | number | boolean | undefined;
        info1?: string | number | boolean | undefined;
        info2?: string | number | boolean | undefined;
        info3?: string | number | boolean | undefined;
        info4?: string | number | boolean | undefined;
        info5?: string | number | boolean | undefined;
        info6?: string | number | boolean | undefined;
        info7?: string | number | boolean | undefined;
        info8?: string | number | boolean | undefined;
        info9?: string | number | boolean | undefined;
        info10?: string | number | boolean | undefined;
        info11?: string | number | boolean | undefined;
        info12?: string | number | boolean | undefined;
        success1?: string | number | boolean | undefined;
        success2?: string | number | boolean | undefined;
        success3?: string | number | boolean | undefined;
        success4?: string | number | boolean | undefined;
        success5?: string | number | boolean | undefined;
        success6?: string | number | boolean | undefined;
        success7?: string | number | boolean | undefined;
        success8?: string | number | boolean | undefined;
        success9?: string | number | boolean | undefined;
        success10?: string | number | boolean | undefined;
        success11?: string | number | boolean | undefined;
        success12?: string | number | boolean | undefined;
        neutral1?: string | number | boolean | undefined;
        neutral2?: string | number | boolean | undefined;
        neutral3?: string | number | boolean | undefined;
        neutral4?: string | number | boolean | undefined;
        neutral5?: string | number | boolean | undefined;
        neutral6?: string | number | boolean | undefined;
        neutral7?: string | number | boolean | undefined;
        neutral8?: string | number | boolean | undefined;
        neutral9?: string | number | boolean | undefined;
        neutral10?: string | number | boolean | undefined;
        neutral11?: string | number | boolean | undefined;
        neutral12?: string | number | boolean | undefined;
        accent1?: string | number | boolean | undefined;
        accent2?: string | number | boolean | undefined;
        accent3?: string | number | boolean | undefined;
        accent4?: string | number | boolean | undefined;
        accent5?: string | number | boolean | undefined;
        accent6?: string | number | boolean | undefined;
        accent7?: string | number | boolean | undefined;
        accent8?: string | number | boolean | undefined;
        accent9?: string | number | boolean | undefined;
        accent10?: string | number | boolean | undefined;
        accent11?: string | number | boolean | undefined;
        accent12?: string | number | boolean | undefined;
        primary1?: string | number | boolean | undefined;
        primary2?: string | number | boolean | undefined;
        primary3?: string | number | boolean | undefined;
        primary4?: string | number | boolean | undefined;
        primary5?: string | number | boolean | undefined;
        primary6?: string | number | boolean | undefined;
        primary7?: string | number | boolean | undefined;
        primary8?: string | number | boolean | undefined;
        primary9?: string | number | boolean | undefined;
        primary10?: string | number | boolean | undefined;
        primary11?: string | number | boolean | undefined;
        primary12?: string | number | boolean | undefined;
        whiteAlpha1?: string | number | boolean | undefined;
        whiteAlpha2?: string | number | boolean | undefined;
        whiteAlpha3?: string | number | boolean | undefined;
        whiteAlpha4?: string | number | boolean | undefined;
        whiteAlpha5?: string | number | boolean | undefined;
        whiteAlpha6?: string | number | boolean | undefined;
        whiteAlpha7?: string | number | boolean | undefined;
        whiteAlpha8?: string | number | boolean | undefined;
        whiteAlpha9?: string | number | boolean | undefined;
        whiteAlpha10?: string | number | boolean | undefined;
        whiteAlpha11?: string | number | boolean | undefined;
        whiteAlpha12?: string | number | boolean | undefined;
        blackAlpha1?: string | number | boolean | undefined;
        blackAlpha2?: string | number | boolean | undefined;
        blackAlpha3?: string | number | boolean | undefined;
        blackAlpha4?: string | number | boolean | undefined;
        blackAlpha5?: string | number | boolean | undefined;
        blackAlpha6?: string | number | boolean | undefined;
        blackAlpha7?: string | number | boolean | undefined;
        blackAlpha8?: string | number | boolean | undefined;
        blackAlpha9?: string | number | boolean | undefined;
        blackAlpha10?: string | number | boolean | undefined;
        blackAlpha11?: string | number | boolean | undefined;
        blackAlpha12?: string | number | boolean | undefined;
    } | undefined;
    space?: {
        px?: string | number | boolean | undefined;
        "0_5"?: string | number | boolean | undefined;
        1?: string | number | boolean | undefined;
        "1_5"?: string | number | boolean | undefined;
        2?: string | number | boolean | undefined;
        "2_5"?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        "3_5"?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
        12?: string | number | boolean | undefined;
        14?: string | number | boolean | undefined;
        16?: string | number | boolean | undefined;
        20?: string | number | boolean | undefined;
        24?: string | number | boolean | undefined;
        28?: string | number | boolean | undefined;
        32?: string | number | boolean | undefined;
        36?: string | number | boolean | undefined;
        40?: string | number | boolean | undefined;
        44?: string | number | boolean | undefined;
        48?: string | number | boolean | undefined;
        52?: string | number | boolean | undefined;
        56?: string | number | boolean | undefined;
        60?: string | number | boolean | undefined;
        64?: string | number | boolean | undefined;
        72?: string | number | boolean | undefined;
        80?: string | number | boolean | undefined;
        96?: string | number | boolean | undefined;
    } | undefined;
    sizes?: {
        prose?: string | number | boolean | undefined;
        max?: string | number | boolean | undefined;
        min?: string | number | boolean | undefined;
        full?: string | number | boolean | undefined;
        screenW?: string | number | boolean | undefined;
        screenH?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        "4xl"?: string | number | boolean | undefined;
        "5xl"?: string | number | boolean | undefined;
        "6xl"?: string | number | boolean | undefined;
        "7xl"?: string | number | boolean | undefined;
        "8xl"?: string | number | boolean | undefined;
        containerSm?: string | number | boolean | undefined;
        containerMd?: string | number | boolean | undefined;
        containerLg?: string | number | boolean | undefined;
        containerXl?: string | number | boolean | undefined;
        container2xl?: string | number | boolean | undefined;
        px?: string | number | boolean | undefined;
        "0_5"?: string | number | boolean | undefined;
        1?: string | number | boolean | undefined;
        "1_5"?: string | number | boolean | undefined;
        2?: string | number | boolean | undefined;
        "2_5"?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        "3_5"?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
        12?: string | number | boolean | undefined;
        14?: string | number | boolean | undefined;
        16?: string | number | boolean | undefined;
        20?: string | number | boolean | undefined;
        24?: string | number | boolean | undefined;
        28?: string | number | boolean | undefined;
        32?: string | number | boolean | undefined;
        36?: string | number | boolean | undefined;
        40?: string | number | boolean | undefined;
        44?: string | number | boolean | undefined;
        48?: string | number | boolean | undefined;
        52?: string | number | boolean | undefined;
        56?: string | number | boolean | undefined;
        60?: string | number | boolean | undefined;
        64?: string | number | boolean | undefined;
        72?: string | number | boolean | undefined;
        80?: string | number | boolean | undefined;
        96?: string | number | boolean | undefined;
    } | undefined;
    fonts?: {
        sans?: string | number | boolean | undefined;
        serif?: string | number | boolean | undefined;
        mono?: string | number | boolean | undefined;
    } | undefined;
    fontSizes?: {
        "2xs"?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        "4xl"?: string | number | boolean | undefined;
        "5xl"?: string | number | boolean | undefined;
        "6xl"?: string | number | boolean | undefined;
        "7xl"?: string | number | boolean | undefined;
        "8xl"?: string | number | boolean | undefined;
        "9xl"?: string | number | boolean | undefined;
    } | undefined;
    fontWeights?: {
        hairline?: string | number | boolean | undefined;
        thin?: string | number | boolean | undefined;
        light?: string | number | boolean | undefined;
        normal?: string | number | boolean | undefined;
        medium?: string | number | boolean | undefined;
        semibold?: string | number | boolean | undefined;
        bold?: string | number | boolean | undefined;
        extrabold?: string | number | boolean | undefined;
        black?: string | number | boolean | undefined;
    } | undefined;
    letterSpacings?: {
        tighter?: string | number | boolean | undefined;
        tight?: string | number | boolean | undefined;
        normal?: string | number | boolean | undefined;
        wide?: string | number | boolean | undefined;
        wider?: string | number | boolean | undefined;
        widest?: string | number | boolean | undefined;
    } | undefined;
    lineHeights?: {
        normal?: string | number | boolean | undefined;
        none?: string | number | boolean | undefined;
        shorter?: string | number | boolean | undefined;
        short?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        tall?: string | number | boolean | undefined;
        taller?: string | number | boolean | undefined;
        3?: string | number | boolean | undefined;
        4?: string | number | boolean | undefined;
        5?: string | number | boolean | undefined;
        6?: string | number | boolean | undefined;
        7?: string | number | boolean | undefined;
        8?: string | number | boolean | undefined;
        9?: string | number | boolean | undefined;
        10?: string | number | boolean | undefined;
    } | undefined;
    radii?: {
        none?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        "3xl"?: string | number | boolean | undefined;
        full?: string | number | boolean | undefined;
    } | undefined;
    shadows?: {
        none?: string | number | boolean | undefined;
        xs?: string | number | boolean | undefined;
        sm?: string | number | boolean | undefined;
        md?: string | number | boolean | undefined;
        lg?: string | number | boolean | undefined;
        xl?: string | number | boolean | undefined;
        "2xl"?: string | number | boolean | undefined;
        inner?: string | number | boolean | undefined;
        outline?: string | number | boolean | undefined;
    } | undefined;
    zIndices?: {
        hide?: string | number | boolean | undefined;
        auto?: string | number | boolean | undefined;
        base?: string | number | boolean | undefined;
        docked?: string | number | boolean | undefined;
        sticky?: string | number | boolean | undefined;
        banner?: string | number | boolean | undefined;
        overlay?: string | number | boolean | undefined;
        modal?: string | number | boolean | undefined;
        dropdown?: string | number | boolean | undefined;
        popover?: string | number | boolean | undefined;
        tooltip?: string | number | boolean | undefined;
        skipLink?: string | number | boolean | undefined;
        notification?: string | number | boolean | undefined;
    } | undefined;
} & {
    [x: string]: {
        [x: string]: string | number | boolean;
        [x: number]: string | number | boolean;
    };
})>(nameOrScalesArg0: Argument0, nameOrScalesArg1?: Argument1 | undefined) => string & {
    className: string;
    selector: string;
} & (Argument0 extends string ? import("@stitches/core/types/stitches").ThemeTokens<Argument1, "hope"> : import("@stitches/core/types/stitches").ThemeTokens<Argument0, "hope">), getCssText: () => string, keyframes: (style: {
    [offset: string]: import("@stitches/core/types/css-util").CSS<{
        sm: string;
        md: string;
        lg: string;
        xl: string;
        "2xl": string;
        "reduce-motion": string;
        light: string;
        dark: string;
    }, {
        colors: {
            loContrast: string;
            background: string;
            focusRing: string;
            closeButtonHoverBackground: string;
            closeButtonActiveBackground: string;
            progressStripe: string;
            danger1: string;
            danger2: string;
            danger3: string;
            danger4: string;
            danger5: string;
            danger6: string;
            danger7: string;
            danger8: string;
            danger9: string;
            danger10: string;
            danger11: string;
            danger12: string;
            warning1: string;
            warning2: string;
            warning3: string;
            warning4: string;
            warning5: string;
            warning6: string;
            warning7: string;
            warning8: string;
            warning9: string;
            warning10: string;
            warning11: string;
            warning12: string;
            info1: string;
            info2: string;
            info3: string;
            info4: string;
            info5: string;
            info6: string;
            info7: string;
            info8: string;
            info9: string;
            info10: string;
            info11: string;
            info12: string;
            success1: string;
            success2: string;
            success3: string;
            success4: string;
            success5: string;
            success6: string;
            success7: string;
            success8: string;
            success9: string;
            success10: string;
            success11: string;
            success12: string;
            neutral1: string;
            neutral2: string;
            neutral3: string;
            neutral4: string;
            neutral5: string;
            neutral6: string;
            neutral7: string;
            neutral8: string;
            neutral9: string;
            neutral10: string;
            neutral11: string;
            neutral12: string;
            accent1: string;
            accent2: string;
            accent3: string;
            accent4: string;
            accent5: string;
            accent6: string;
            accent7: string;
            accent8: string;
            accent9: string;
            accent10: string;
            accent11: string;
            accent12: string;
            primary1: string;
            primary2: string;
            primary3: string;
            primary4: string;
            primary5: string;
            primary6: string;
            primary7: string;
            primary8: string;
            primary9: string;
            primary10: string;
            primary11: string;
            primary12: string;
            whiteAlpha1: string;
            whiteAlpha2: string;
            whiteAlpha3: string;
            whiteAlpha4: string;
            whiteAlpha5: string;
            whiteAlpha6: string;
            whiteAlpha7: string;
            whiteAlpha8: string;
            whiteAlpha9: string;
            whiteAlpha10: string;
            whiteAlpha11: string;
            whiteAlpha12: string;
            blackAlpha1: string;
            blackAlpha2: string;
            blackAlpha3: string;
            blackAlpha4: string;
            blackAlpha5: string;
            blackAlpha6: string;
            blackAlpha7: string;
            blackAlpha8: string;
            blackAlpha9: string;
            blackAlpha10: string;
            blackAlpha11: string;
            blackAlpha12: string;
        };
        space: {
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        sizes: {
            prose: string;
            max: string;
            min: string;
            full: string;
            screenW: string;
            screenH: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            containerSm: string;
            containerMd: string;
            containerLg: string;
            containerXl: string;
            container2xl: string;
            px: string;
            "0_5": string;
            "1": string;
            "1_5": string;
            "2": string;
            "2_5": string;
            "3": string;
            "3_5": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
            "12": string;
            "14": string;
            "16": string;
            "20": string;
            "24": string;
            "28": string;
            "32": string;
            "36": string;
            "40": string;
            "44": string;
            "48": string;
            "52": string;
            "56": string;
            "60": string;
            "64": string;
            "72": string;
            "80": string;
            "96": string;
        };
        fonts: {
            sans: string;
            serif: string;
            mono: string;
        };
        fontSizes: {
            "2xs": string;
            xs: string;
            sm: string;
            base: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            "4xl": string;
            "5xl": string;
            "6xl": string;
            "7xl": string;
            "8xl": string;
            "9xl": string;
        };
        fontWeights: {
            hairline: number;
            thin: number;
            light: number;
            normal: number;
            medium: number;
            semibold: number;
            bold: number;
            extrabold: number;
            black: number;
        };
        letterSpacings: {
            tighter: string;
            tight: string;
            normal: string;
            wide: string;
            wider: string;
            widest: string;
        };
        lineHeights: {
            normal: string;
            none: number;
            shorter: number;
            short: number;
            base: number;
            tall: number;
            taller: number;
            "3": string;
            "4": string;
            "5": string;
            "6": string;
            "7": string;
            "8": string;
            "9": string;
            "10": string;
        };
        radii: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            "3xl": string;
            full: string;
        };
        shadows: {
            none: string;
            xs: string;
            sm: string;
            md: string;
            lg: string;
            xl: string;
            "2xl": string;
            inner: string;
            outline: string;
        };
        zIndices: {
            hide: number;
            auto: string;
            base: number;
            docked: number;
            sticky: number;
            banner: number;
            overlay: number;
            modal: number;
            dropdown: number;
            popover: number;
            tooltip: number;
            skipLink: number;
            notification: number;
        };
    }, {
        borderWidth: "sizes";
        borderTopWidth: "sizes";
        borderRightWidth: "sizes";
        borderBottomWidth: "sizes";
        borderLeftWidth: "sizes";
        strokeWidth: "sizes";
        gap: "space";
        gridGap: "space";
        columnGap: "space";
        gridColumnGap: "space";
        rowGap: "space";
        gridRowGap: "space";
        inset: "space";
        insetBlock: "space";
        insetBlockEnd: "space";
        insetBlockStart: "space";
        insetInline: "space";
        insetInlineEnd: "space";
        insetInlineStart: "space";
        margin: "space";
        marginTop: "space";
        marginRight: "space";
        marginBottom: "space";
        marginLeft: "space";
        marginBlock: "space";
        marginBlockEnd: "space";
        marginBlockStart: "space";
        marginInline: "space";
        marginInlineEnd: "space";
        marginInlineStart: "space";
        padding: "space";
        paddingTop: "space";
        paddingRight: "space";
        paddingBottom: "space";
        paddingLeft: "space";
        paddingBlock: "space";
        paddingBlockEnd: "space";
        paddingBlockStart: "space";
        paddingInline: "space";
        paddingInlineEnd: "space";
        paddingInlineStart: "space";
        scrollMargin: "space";
        scrollMarginTop: "space";
        scrollMarginRight: "space";
        scrollMarginBottom: "space";
        scrollMarginLeft: "space";
        scrollMarginBlock: "space";
        scrollMarginBlockEnd: "space";
        scrollMarginBlockStart: "space";
        scrollMarginInline: "space";
        scrollMarginInlineEnd: "space";
        scrollMarginInlineStart: "space";
        scrollPadding: "space";
        scrollPaddingTop: "space";
        scrollPaddingRight: "space";
        scrollPaddingBottom: "space";
        scrollPaddingLeft: "space";
        scrollPaddingBlock: "space";
        scrollPaddingBlockEnd: "space";
        scrollPaddingBlockStart: "space";
        scrollPaddingInline: "space";
        scrollPaddingInlineEnd: "space";
        scrollPaddingInlineStart: "space";
        top: "space";
        right: "space";
        bottom: "space";
        left: "space";
        fontSize: "fontSizes";
        background: "colors";
        backgroundColor: "colors";
        backgroundImage: "colors";
        borderImage: "colors";
        border: "colors";
        borderBlock: "colors";
        borderBlockEnd: "colors";
        borderBlockStart: "colors";
        borderBottom: "colors";
        borderBottomColor: "colors";
        borderColor: "colors";
        borderInline: "colors";
        borderInlineEnd: "colors";
        borderInlineStart: "colors";
        borderLeft: "colors";
        borderLeftColor: "colors";
        borderRight: "colors";
        borderRightColor: "colors";
        borderTop: "colors";
        borderTopColor: "colors";
        caretColor: "colors";
        color: "colors";
        columnRuleColor: "colors";
        outline: "colors";
        outlineColor: "colors";
        fill: "colors";
        stroke: "colors";
        textDecorationColor: "colors";
        fontFamily: "fonts";
        fontWeight: "fontWeights";
        lineHeight: "lineHeights";
        letterSpacing: "letterSpacings";
        blockSize: "sizes";
        minBlockSize: "sizes";
        maxBlockSize: "sizes";
        inlineSize: "sizes";
        minInlineSize: "sizes";
        maxInlineSize: "sizes";
        width: "sizes";
        minWidth: "sizes";
        maxWidth: "sizes";
        height: "sizes";
        minHeight: "sizes";
        maxHeight: "sizes";
        flexBasis: "sizes";
        gridTemplateColumns: "sizes";
        gridTemplateRows: "sizes";
        borderStyle: "borderStyles";
        borderTopStyle: "borderStyles";
        borderLeftStyle: "borderStyles";
        borderRightStyle: "borderStyles";
        borderBottomStyle: "borderStyles";
        borderRadius: "radii";
        borderTopLeftRadius: "radii";
        borderTopRightRadius: "radii";
        borderBottomRightRadius: "radii";
        borderBottomLeftRadius: "radii";
        boxShadow: "shadows";
        textShadow: "shadows";
        transition: "transitions";
        zIndex: "zIndices";
    }, {
        noOfLines: (value: string | number) => {
            overflow: string;
            display: string;
            "-webkit-box-orient": string;
            "-webkit-line-clamp": string | number;
        };
        w: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
        };
        minW: (value: {
            readonly [$$PropertyValue]: "minWidth";
        }) => {
            minWidth: {
                readonly [$$PropertyValue]: "minWidth";
            };
        };
        maxW: (value: {
            readonly [$$PropertyValue]: "maxWidth";
        }) => {
            maxWidth: {
                readonly [$$PropertyValue]: "maxWidth";
            };
        };
        h: (value: {
            readonly [$$PropertyValue]: "height";
        }) => {
            height: {
                readonly [$$PropertyValue]: "height";
            };
        };
        minH: (value: {
            readonly [$$PropertyValue]: "minHeight";
        }) => {
            minHeight: {
                readonly [$$PropertyValue]: "minHeight";
            };
        };
        maxH: (value: {
            readonly [$$PropertyValue]: "maxHeight";
        }) => {
            maxHeight: {
                readonly [$$PropertyValue]: "maxHeight";
            };
        };
        boxSize: (value: {
            readonly [$$PropertyValue]: "width";
        }) => {
            width: {
                readonly [$$PropertyValue]: "width";
            };
            height: {
                readonly [$$PropertyValue]: "width";
            };
        };
        shadow: (value: {
            readonly [$$PropertyValue]: "boxShadow";
        }) => {
            boxShadow: {
                readonly [$$PropertyValue]: "boxShadow";
            };
        };
        p: (value: {
            readonly [$$PropertyValue]: "padding";
        }) => {
            padding: {
                readonly [$$PropertyValue]: "padding";
            };
        };
        pt: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        pr: (value: {
            readonly [$$PropertyValue]: "paddingRight";
        }) => {
            paddingRight: {
                readonly [$$PropertyValue]: "paddingRight";
            };
        };
        paddingStart: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        ps: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        pb: (value: {
            readonly [$$PropertyValue]: "paddingBottom";
        }) => {
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingBottom";
            };
        };
        pl: (value: {
            readonly [$$PropertyValue]: "paddingLeft";
        }) => {
            paddingLeft: {
                readonly [$$PropertyValue]: "paddingLeft";
            };
        };
        pe: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        paddingEnd: (value: {
            readonly [$$PropertyValue]: "paddingInlineEnd";
        }) => {
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineEnd";
            };
        };
        px: (value: {
            readonly [$$PropertyValue]: "paddingInlineStart";
        }) => {
            paddingInlineStart: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
            paddingInlineEnd: {
                readonly [$$PropertyValue]: "paddingInlineStart";
            };
        };
        py: (value: {
            readonly [$$PropertyValue]: "paddingTop";
        }) => {
            paddingTop: {
                readonly [$$PropertyValue]: "paddingTop";
            };
            paddingBottom: {
                readonly [$$PropertyValue]: "paddingTop";
            };
        };
        m: (value: {
            readonly [$$PropertyValue]: "margin";
        }) => {
            margin: {
                readonly [$$PropertyValue]: "margin";
            };
        };
        mt: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        mr: (value: {
            readonly [$$PropertyValue]: "marginRight";
        }) => {
            marginRight: {
                readonly [$$PropertyValue]: "marginRight";
            };
        };
        marginStart: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        ms: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        mb: (value: {
            readonly [$$PropertyValue]: "marginBottom";
        }) => {
            marginBottom: {
                readonly [$$PropertyValue]: "marginBottom";
            };
        };
        ml: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            marginLeft: {
                readonly [$$PropertyValue]: "marginLeft";
            };
        };
        marginEnd: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        me: (value: {
            readonly [$$PropertyValue]: "marginInlineEnd";
        }) => {
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineEnd";
            };
        };
        mx: (value: {
            readonly [$$PropertyValue]: "marginInlineStart";
        }) => {
            marginInlineStart: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
            marginInlineEnd: {
                readonly [$$PropertyValue]: "marginInlineStart";
            };
        };
        my: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            marginTop: {
                readonly [$$PropertyValue]: "marginTop";
            };
            marginBottom: {
                readonly [$$PropertyValue]: "marginTop";
            };
        };
        spaceX: (value: {
            readonly [$$PropertyValue]: "marginLeft";
        }) => {
            "& > * + *": {
                marginLeft: {
                    readonly [$$PropertyValue]: "marginLeft";
                };
            };
        };
        spaceY: (value: {
            readonly [$$PropertyValue]: "marginTop";
        }) => {
            "& > * + *": {
                marginTop: {
                    readonly [$$PropertyValue]: "marginTop";
                };
            };
        };
        borderTopRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderRightRadius: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        borderStartRadius: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        borderBottomRadius: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        borderLeftRadius: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        borderEndRadius: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        rounded: (value: {
            readonly [$$PropertyValue]: "borderRadius";
        }) => {
            borderRadius: {
                readonly [$$PropertyValue]: "borderRadius";
            };
        };
        roundedTop: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedRight: (value: {
            readonly [$$PropertyValue]: "borderTopRightRadius";
        }) => {
            borderTopRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderTopRightRadius";
            };
        };
        roundedStart: (value: {
            readonly [$$PropertyValue]: "borderStartStartRadius";
        }) => {
            borderStartStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
            borderEndStartRadius: {
                readonly [$$PropertyValue]: "borderStartStartRadius";
            };
        };
        roundedBottom: (value: {
            readonly [$$PropertyValue]: "borderBottomLeftRadius";
        }) => {
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
            borderBottomRightRadius: {
                readonly [$$PropertyValue]: "borderBottomLeftRadius";
            };
        };
        roundedLeft: (value: {
            readonly [$$PropertyValue]: "borderTopLeftRadius";
        }) => {
            borderTopLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
            borderBottomLeftRadius: {
                readonly [$$PropertyValue]: "borderTopLeftRadius";
            };
        };
        roundedEnd: (value: {
            readonly [$$PropertyValue]: "borderStartEndRadius";
        }) => {
            borderStartEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
            borderEndEndRadius: {
                readonly [$$PropertyValue]: "borderStartEndRadius";
            };
        };
        _hover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:hover, &[data-hover]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _active: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:active, &[data-active]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus, &[data-focus]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _highlighted: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-highlighted]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-within": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _focusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:focus-visible": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _disabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[disabled], &[aria-disabled=true], &[data-disabled]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _readOnly: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-readonly=true], &[readonly], &[data-readonly]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _before: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::before": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _after: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::after": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _empty: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:empty": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _expanded: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-expanded=true], &[data-expanded]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _checked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-checked=true], &[data-checked]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _grabbed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-grabbed=true], &[data-grabbed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _pressed: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-pressed=true], &[data-pressed]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _invalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-invalid=true], &[data-invalid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _valid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-valid], &[data-state=valid]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _loading: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[data-loading], &[aria-busy=true]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-selected=true], &[data-selected]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _hidden: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[hidden], &[data-hidden]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _even: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(even)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _odd: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:nth-of-type(odd)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _first: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:first-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _last: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:last-of-type": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notFirst: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:first-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _notLast: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:not(:last-of-type)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _visited: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:visited": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeLink: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=page]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _activeStep: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&[aria-current=step]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _indeterminate: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerHover: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocus: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusVisible: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerActive: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerSelected: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerDisabled: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerInvalid: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerChecked: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _groupFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerFocusWithin: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _peerPlaceholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            [x: string]: import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholder: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::placeholder": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _placeholderShown: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:placeholder-shown": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _fullScreen: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&:fullscreen": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _selection: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "&::selection": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaDark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-color-scheme: dark)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _mediaReduceMotion: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            "@media (prefers-reduced-motion: reduce)": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _dark: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-dark &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        _light: (value: import("@stitches/core").CSS<{
            media: {};
            theme: {};
            themeMap: {};
            utils: {};
        }>) => {
            ".hope-ui-light &": import("@stitches/core").CSS<{
                media: {};
                theme: {};
                themeMap: {};
                utils: {};
            }>;
        };
        pos: (value: {
            readonly [$$PropertyValue]: "position";
        }) => {
            position: {
                readonly [$$PropertyValue]: "position";
            };
        };
        d: (value: {
            readonly [$$PropertyValue]: "display";
        }) => {
            display: {
                readonly [$$PropertyValue]: "display";
            };
        };
        borderX: (value: {
            readonly [$$PropertyValue]: "borderLeft";
        }) => {
            borderLeft: {
                readonly [$$PropertyValue]: "borderLeft";
            };
            borderRight: {
                readonly [$$PropertyValue]: "borderLeft";
            };
        };
        borderY: (value: {
            readonly [$$PropertyValue]: "borderTop";
        }) => {
            borderTop: {
                readonly [$$PropertyValue]: "borderTop";
            };
            borderBottom: {
                readonly [$$PropertyValue]: "borderTop";
            };
        };
        bg: (value: {
            readonly [$$PropertyValue]: "background";
        }) => {
            background: {
                readonly [$$PropertyValue]: "background";
            };
        };
        bgColor: (value: {
            readonly [$$PropertyValue]: "backgroundColor";
        }) => {
            backgroundColor: {
                readonly [$$PropertyValue]: "backgroundColor";
            };
        };
    }>;
}) => {
    (): string;
    name: string;
};
//# sourceMappingURL=stitches.config.d.ts.map