import { createSignal, createEffect, onMount, onCleanup } from solid-js';
import { create<PERSON>hart, IChartApi, ISeriesApi, LineData, HistogramData } from lightweight-charts';
import { css } from '../../styled-system/css';

export interface BacktestResult {
  totalReturn: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  trades: Trade[];
  equity: EquityPoint[];
  monthlyReturns: MonthlyReturn[];
  drawdownSeries: DrawdownPoint[];
}

export interface Trade {
  timestamp: string;
  symbol: string;
  side: buy' |sell;
  quantity: number;
  price: number;
  pnl?: number;
}

export interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdown: number;
}

export interface MonthlyReturn {
  month: string;
  return: number;
}

export interface DrawdownPoint {
  timestamp: string;
  drawdown: number;
}

interface BacktestChartsProps {
  result: BacktestResult;
  height?: number;
}

export default function BacktestCharts(props: BacktestChartsProps) {
  let equityChartContainer: HTMLDivElement;
  let drawdownChartContainer: HTMLDivElement;
  let returnsHistogramContainer: HTMLDivElement;
  let monthlyHeatmapContainer : HTMLDivElement;

  const [equityChart, setEquityChart] = createSignal<IChartApi | null>(null);
  const [drawdownChart, setDrawdownChart] = createSignal<IChartApi | null>(null);
  const [activeTab, setActiveTab] = createSignal<equity' |drawdown' |returns' |monthly'>(equity);

  // 初始化权益曲线图表
  const initEquityChart = () => {
    if (!equityChartContainer) return;

    const chart = createChart(equityChartContainer, {
      width: equityChartContainer.clientWidth,
      height: props.height || 400,
      layout: { ''
        background: { color: #ffffff }, '''
       textColor:  #333,
      },''
      grid: { '''
        vertLines: { color: '#f0f0f0 }, '''
        horzLines: { color: '#f0f0f0 }, ''
      },'
      rightPriceScale: { '''
       borderColor: #cccccc,
      },''
      timeScale: { '''
       borderColor: #cccccc,
        timeVisible: true,
        secondsVisible: false,
      },
    });
''
    const equitySeries = chart.addLineSeries({ '''
     color: '#1890ff,
      lineWidth: 2,
     title:  权益曲线,
    });

    // 转换数据格式
    const equityData: LineData[] = props.result.equity.map(point => ({
      time: new Date(point.timestamp).getTime() / 1000,
      value: point.equity,
    }));

    equitySeries.setData(equityData);
    setEquityChart(chart);

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chart.applyOptions({
        width: equityChartContainer.clientWidth,
      });
    });
    resizeObserver.observe(equityChartContainer);

    onCleanup(() => {
      resizeObserver.disconnect();
      chart.remove();
    });
  };

  // 初始化回撤图表
  const initDrawdownChart = () => {
    if (!drawdownChartContainer) return;

    const chart = createChart(drawdownChartContainer, {
      width: drawdownChartContainer.clientWidth,
      height: props.height || 400,
      layout: { ''
        background: { color: #ffffff }, '''
       textColor:  #333,
      },''
      grid: { '''
        vertLines: { color: '#f0f0f0 }, '''
        horzLines: { color: '#f0f0f0 }, ''
      },'
      rightPriceScale: { '''
       borderColor: #cccccc,
      },''
      timeScale: { '''
       borderColor: #cccccc,
        timeVisible: true,
        secondsVisible: false,
      },
    });
''
    const drawdownSeries = chart.addAreaSeries({ '''
     topColor: rgba(255, 77, 79, 0.3), ''
     bottomColor:  rgba(255, 77, 79, 0.1), ''
     lineColor:  #ff4d4f,
      lineWidth: 2,
     title:  回撤曲线,
    });

    // 转换数据格式
    const drawdownData: LineData[] = props.result.drawdownSeries?.map(point => ({
      time: new Date(point.timestamp).getTime() / 1000,
      value: point.drawdown,
    })) || [];

    drawdownSeries.setData(drawdownData);
    setDrawdownChart(chart);

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chart.applyOptions({
        width : drawdownChartContainer.clientWidth,
      });
    });
    resizeObserver.observe(drawdownChartContainer);

    onCleanup(() => {
      resizeObserver.disconnect();
      chart.remove();
    });
  };

  // 渲染收益分布直方图
  const renderReturnsHistogram = () => {
    if (!returnsHistogramContainer) return;

    // 计算日收益率
    const dailyReturns = [];
    for (let i = 1; i < props.result.equity.length; i++) {
      const prevEquity = props.result.equity[i - 1].equity;
      const currentEquity = props.result.equity[i].equity;
      const dailyReturn = (currentEquity - prevEquity) / prevEquity;
      dailyReturns.push(dailyReturn);
    }

    // 创建直方图数据
    const bins = 20;
    const min = Math.min(...dailyReturns);
    const max = Math.max(...dailyReturns);
    const binWidth = (max - min) / bins;
    const histogram = new Array(bins).fill(0);

    dailyReturns.forEach(ret => {
      const binIndex = Math.min(Math.floor((ret - min) / binWidth), bins - 1);
      histogram[binIndex]++;
    });

    // 使用Canvas绘制直方图
    const canvas = document.createElement(canvas);
    canvas.width = returnsHistogramContainer.clientWidth;
    canvas.height = props.height || 400;
    const ctx = canvas.getContext('2d)!;

    // 清空容器并添加canvas
    returnsHistogramContainer.innerHTML =;
    returnsHistogramContainer.appendChild(canvas);

    // 绘制直方图
    const maxCount = Math.max(...histogram);
    const barWidth = canvas.width / bins;

    ctx.fillStyle =#1890ff;
    histogram.forEach((count, i) => {
      const barHeight = (count / maxCount) * (canvas.height - 40);
      const x = i * barWidth;
      const y = canvas.height - barHeight - 20;
      
      ctx.fillRect(x, y, barWidth - 2, barHeight);
    });

    // 添加标签''
    ctx.fillStyle =#666;
    ctx.font ='12px Arial';
    ctx.textAlign =center';
    
    for (let i = 0; i <= bins; i += 5) {
      const x = i * barWidth;
      const value = (min + i * binWidth) * 100;
      ctx.fillText(`${value.toFixed(1)}%`, x, canvas.height - 5);
    }
  };

  // 渲染月度收益热力图
  const renderMonthlyHeatmap = () => {
    if (!monthlyHeatmapContainer || !props.result.monthlyReturns) return;

    const container = monthlyHeatmapContainer;
    container.innerHTML =`;

    // 创建热力图容器
    const heatmapDiv = document.createElement(div);
    heatmapDiv.style.cssText = `
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 2px;
      padding: 20px;
      max-width: 800px;
      margin : 0 auto;
    `;`
```
    const months = [1月,2月,3月,4月,5月,6月,7月,8月,9月,10月,11月,12月`];
    
    // 获取收益率范围用于颜色映射
    const returns = props.result.monthlyReturns.map(m => m.return);
    const minReturn = Math.min(...returns);
    const maxReturn = Math.max(...returns);

    // 创建月度格子
    props.result.monthlyReturns.forEach((monthData, index) => {
      const cell = document.createElement(div);
      const normalizedReturn = (monthData.return - minReturn) / (maxReturn - minReturn);
      
      // 颜色映射：红色(负收益) -> 白色(零收益) -> 绿色(正收益)
      let backgroundColor;
      if (monthData.return < 0) {
        const intensity = Math.abs(monthData.return) / Math.abs(minReturn);
        backgroundColor = `rgba(255, 77, 79, ${intensity * 0.8})`;
      } else {
        const intensity = monthData.return / maxReturn;
        backgroundColor = `rgba(82, 196, 26, ${intensity * 0.8})`;
      }

      cell.style.cssText = `
        background-color: ${backgroundColor};
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        padding: 8px;
        text-align: center;
        font-size: 12px;
        min-height: 40px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s;
      `;
`
      cell.innerHTML = ``
        <div style=font-weight: bold; margin-bottom: 2px;>${months[index % 12]}</div>"""
        <div style=color: ${monthData.return >= 0 ? #52c41a: #ff4d4f};'>'`
          ${monthData.return >= 0 ? `+:`}${(monthData.return * 100).toFixed(1)}%`
        </div>`
      `;
`
      cell.addEventListener(mouseenter', () => {'''''
        cell.style.transform =scale(1.05)';
      });
'''''
      cell.addEventListener(mouseleave', () => {'''''
        cell.style.transform =scale(1)';
      });

      heatmapDiv.appendChild(cell);
    });

    container.appendChild(heatmapDiv);
  };

  onMount(() => {
    // 根据当前选中的标签页初始化对应图表
    createEffect(() => {
      const tab = activeTab();
      if (tab ===equity) {''
        setTimeout(initEquityChart, 100);
      } else if (tab ===drawdown') {''
        setTimeout(initDrawdownChart, 100);
      } else if (tab ===returns') {''
        setTimeout(renderReturnsHistogram, 100);
      } else if (tab ===monthly') {'
        setTimeout(renderMonthlyHeatmap, 100);
      }
    });
  });

  return (''
    <div class={css({ '''
     bg: white,
     borderRadius: '12px,
     p: '24px,
      boxShadow:  0 2px 8px rgba(0,0,0,0.1)
    })}>''
      {/* 标签页导航 */}
      <div class={css({ ''''
       display', flex: $4,
       borderBottom: '1px solid #f0f0f0,
        mb: ''24px
      })}>'''
        {['''
          { key:  equity, label: '权益曲线 }, '''
          { key:  drawdown, label: '回撤分析 }, '`
          { key:  returns, label: `收益分布 },``
          { key:  monthly, label: '月度热力图 }
        ].map(tab => (
          <button''
            class={css({ '''
             px: '16px,
             py: '12px,
             border: none,
             bg: transparent,
             cursor: pointer,
             fontSize: '14px,
              fontWeight: activeTab() === tab.key ? '600: '400,
              color: activeTab() === tab.key ? '#1890ff: '#666,
              borderBottom: activeTab() === tab.key ? '2px solid #1890ff: '2px solid transparent,
             transition: all 0.3s ease,
              _hover: { '''
                color: `#1890ff
              }
            })}
            onClick={() => setActiveTab(tab.key as any)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 图表内容区域 */}
      <div class={css({ minHeight: `${props.height || 400}px` })}>
        {/* 权益曲线图 */}
        <div `
          ref={equityChartContainer!}
          style={{ display: activeTab() ===equity' ?block: none }}
        />

        {/* 回撤图 */}
        <div ''
          ref={drawdownChartContainer!}
          style={{ display: activeTab() ===drawdown' ?block: none }}
        />

        {/* 收益分布直方图 */}
        <div ''
          ref={returnsHistogramContainer!}
          style={{ display: activeTab() ===returns' ?block: none }}
        />

        {/* 月度收益热力图 */}
        <div ''
          ref={monthlyHeatmapContainer!}
          style={{ display: activeTab() ===monthly' ?block: none }}
        />
      </div>
    </div>''
  );'`
}