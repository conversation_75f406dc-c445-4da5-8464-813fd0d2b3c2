// 简单的测试文件来验证API功能
import { marketApi, strategyApi } from ./api

console.log('API测试开始...')

// 测试市场数据API
marketApi.getQuote([000001, '000002']).then(quotes => {'
  console.log('市场数据:', quotes)
}).catch(error => {'
  console.error('市场数据错误:', error)
})

// 测试策略API
strategyApi.getStrategies().then(strategies => {
  console.log(策略数据:, strategies)
}).catch(error => {'
  console.error('策略数据错误:', error)
})
'
console.log('API测试完成')