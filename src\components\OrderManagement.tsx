import { createSignal, createEffect, onCleanup, For, Show, createMemo } from 'solid-js';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import numeral from 'numeral';
import { v4 as uuidv4 } from 'uuid';

export interface Order {
  id: string;
  orderNo: string;
  symbol: string;
  direction: 'buy' |'sell';
  orderType: 'market' |'limit' |'stop' |'takeProfit';
  quantity: number;
  price: number;
  stopPrice?: number;
  status: 'pending' |'filled' |'cancelled' |'partiallyFilled;
  filledQuantity: number;
  averagePrice: number;
  createTime: number;
  updateTime: number;
  commission: number;
  filledAmount?: number;
  notes?: string;
}

export interface Position {
  symbol: string;
  quantity: number;
  averageCost: number;
  currentPrice: number;
  unrealizedPnL: number;
  realizedPnL: number;
  totalValue: number;
  lastUpdate: number;
}

interface OrderManagementProps {
  orders?: Order[];
  positions?: Position[];
  onCreateOrder?: (order: Partial<Order>) => Promise<void>;
  onCancelOrder?: (orderId: string) => Promise<void>;
  onModifyOrder?: (orderId: string, changes: Partial<Order>) => Promise<void>;
  onClosePosition?: (symbol: string, quantity?: number) => Promise<void>;
  onCancelAllOrders?: () => Promise<void>;
  onRefreshOrders?: () => Promise<void>;
  theme?: 'light' |'dark';
  tradingEnabled?: boolean;
  loading?: boolean;
}

export default function OrderManagement(props: OrderManagementProps) {'
  const [activeTab, setActiveTab] = createSignal<'pending' |'filled' |'cancelled'>('pending');
  const [selectedOrder, setSelectedOrder] = createSignal<Order | null>(null);
  const [selectedPosition, setSelectedPosition] = createSignal<Position | null>(null);
  const [modifyDialogVisible, setModifyDialogVisible] = createSignal(false);
  const [detailDialogVisible, setDetailDialogVisible] = createSignal(false);
  const [modifying, setModifying] = createSignal(false);
  
  // 修改订单表单
  const [modifyForm, setModifyForm] = createSignal({
    orderNo: , '
    symbol: ',
    price: 0,
    quantity: 0,
  });

  const [isSubmitting, setIsSubmitting] = createSignal(false);
  const [updateTime, setUpdateTime] = createSignal(Date.now());

  // 定时更新时间戳
  const timeInterval = setInterval(() => {
    setUpdateTime(Date.now());
  }, 1000);

  onCleanup(() => {
    clearInterval(timeInterval);
  });

  // 计算属性 - 按状态分组的订单
  const pendingOrders = createMemo(() => {
    return props.orders?.filter(order => order.status ===pending || order.status ==='partiallyFilled') || [];
  });

  const filledOrders = createMemo(() => {'
    return props.orders?.filter(order => order.status ==='filled') || [];
  });

  const cancelledOrders = createMemo(() => {'
    return props.orders?.filter(order => order.status ==='cancelled') || [];
  });

  const currentOrders = createMemo(() => {
    switch (activeTab()) {'
      case'pending' :
        return pendingOrders();
      case'filled' :
        return filledOrders();
      case'cancelled' : return cancelledOrders();
      default:
        return [];
    }
  });

  // 格式化价格
  const formatPrice = (price: number) => {
    return numeral(price).format(0,0.00);
  };

  // 格式化百分比
  const formatPercent = (value: number) => {
    const sign = value >= 0 ?+: ;
    return `${sign}${numeral(value).format('0.00')}%`;
  };

  // 获取订单状态颜色
  const getOrderStatusColor = (status: Order[status]) => {
    const colors = {'
      pending: props.theme ==='dark' ?'#f59e0b: '#d97706',
      filled: props.theme ==='dark' ?#10b981: '#059669',
      cancelled: props.theme ==='dark' ?'#ef4444: '#dc2626',
      partial: props.theme ==='dark' ?#3b82f6: '#2563eb',
    };
    return colors[status];
  };

  // 获取盈亏颜色
  const getPnLColor = (pnl: number) => {
    if (pnl > 0) return props.theme ===dark ?#10b981: '#059669';
    if (pnl < 0) return props.theme ==='dark' ?'#ef4444: '#dc2626';
    return props.theme ==='dark' ?#9ca3af: '#6b7280';
  };

  // 获取订单类型显示文本
  const getOrderTypeText = (type: Order[orderType]) => {
    const texts = { 
      market: '市价',
      limit: '限价',
      stop: '止损',
      takeProfit: '止盈',
    };
    return texts[type];
  };

  // 获取订单状态显示文本
  const getOrderStatusText = (status: Order[status]) => {
    const texts = { 
      pending: '待成交',
      filled: '已成交',
      cancelled: '已撤销',
      partiallyFilled: '部分成交',
    };
    return texts[status];
  };

  // 格式化时间
  const formatTime = (timestamp: number): string => {
    return format(timestamp,yyyy-MM-dd HH:mm:ss, { locale: zhCN });
  };

  // 刷新订单
  const handleRefreshOrders = async () => {
    if (props.onRefreshOrders) {
      try {
        await props.onRefreshOrders();
      } catch (error) {
        console.error(刷新订单失败:, error);
        alert('刷新订单失败，请重试');
      }
    }
  };

  // 撤销所有订单
  const handleCancelAllOrders = async () => {
    if (!props.onCancelAllOrders) return;

    const confirmed = confirm(确定要撤销所有待成交订单吗？);
    if (!confirmed) return;

    try {
      await props.onCancelAllOrders();
    } catch (error) {'
      console.error('撤销所有订单失败:', error);
      alert('撤销所有订单失败，请重试');
    }
  };

  // 修改订单
  const handleModifyOrder = (order: Order) => {
    setModifyForm({
      orderNo: order.orderNo,
      symbol: order.symbol,
      price: order.price,
      quantity: order.quantity,
    });
    setSelectedOrder(order);
    setModifyDialogVisible(true);
  };

  // 确认修改订单
  const handleConfirmModify = async () => {
    if (!props.onModifyOrder || !selectedOrder()) return;

    const form = modifyForm();
    if (form.price <= 0 || form.quantity <= 0) {
      alert(请输入有效的价格和数量);
      return;
    }

    setModifying(true);
    try {
      await props.onModifyOrder(selectedOrder()!.id, {
        price: form.price,
        quantity: form.quantity,
      });
      setModifyDialogVisible(false);
    } catch (error) {'
      console.error('修改订单失败:', error);
      alert('修改订单失败，请重试');
    } finally {
      setModifying(false);
    }
  };

  // 查看订单详情
  const handleViewOrderDetail = (order: Order) => {
    setSelectedOrder(order);
    setDetailDialogVisible(true);
  };

  // 取消订单
  const handleCancelOrder = async (orderId: string) => {
    if (!props.onCancelOrder) return;
    
    const confirmed = confirm(确认取消此订单？);
    if (!confirmed) return;

    try {
      await props.onCancelOrder(orderId);
    } catch (error) {'
      console.error('取消订单失败:', error);
      alert('取消订单失败，请重试');
    }
  };

  // 平仓
  const handleClosePosition = async (symbol: string, quantity?: number) => {
    if (!props.onClosePosition) return;
    
    const confirmed = confirm(`确认平仓 ${symbol} ${quantity ? quantity +股: 全部}？`);
    if (!confirmed) return;

    try {
      await props.onClosePosition(symbol, quantity);
    } catch (error) {'
      console.error('平仓失败:', error);
      alert('平仓失败，请重试');
    }
  };

  return (
    <div style={{'
      background: props.theme ==='dark' ?#1f2937: '#ffffff',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      padding: '20px',
    }}>
      {/* 管理头部 */}
      <div style={{ 
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '16px',
       'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#ebeef5'}`,
      });
        <div style={{ display: 'flex', gap: '0' }}>
          <button'
            onClick={() => setActiveTab('pending')}
            style={{ 
              padding: '8px 16px',
              border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
              borderRight: 'none',
              borderRadius: '6px 0 0 6px',
              background: activeTab() ==='pending' ?'#3b82f6': (props.theme ==='dark' ?#374151: '#f9fafb'),'
              color: activeTab() ==='pending' ?'white' : (props.theme ==='dark' ?'#f9fafb: '#374151'),'
              cursor: 'pointer',
              fontWeight: '500',
            }}
          >
            待成交 ({pendingOrders().length})
          </button>
          <button'
            onClick={() => setActiveTab('filled')}
            style={{ 
              padding: '8px 16px',
              border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
              borderRight: 'none',
              background: activeTab() ==='filled' ?'#3b82f6': (props.theme ==='dark' ?#374151: '#f9fafb'),'
              color: activeTab() ==='filled' ?'white' : (props.theme ==='dark' ?'#f9fafb: '#374151'),'
              cursor: 'pointer',
              fontWeight: '500',
            }}
          >
            已成交 ({filledOrders().length})
          </button>
          <button'
            onClick={() => setActiveTab('cancelled')}
            style={{ 
              padding: '8px 16px',
              border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
              borderRadius: '0 6px 6px 0',
              background: activeTab() ==='cancelled' ?'#3b82f6': (props.theme ==='dark' ?#374151: '#f9fafb'),'
              color: activeTab() ==='cancelled' ?'white' : (props.theme ==='dark' ?'#f9fafb: '#374151'),'
              cursor: 'pointer',
              fontWeight: '500',
            }}
          >
            已撤销 ({cancelledOrders().length})
          </button>
        </div>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={handleRefreshOrders}
            disabled={props.loading}
            style={{ 
              padding: '8px 16px',
              borderRadius: '6px',
              border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
              background: props.theme ==='dark' ?#374151: '#f9fafb',
              color: props.theme ==='dark' ?'#f9fafb: '#374151',
              cursor: props.loading ?not-allowed: 'pointer',
              fontWeight: '500',
            }}
          >
            {props.loading ?刷新中...: '🔄 刷新'}
          </button>
          <button
            onClick={handleCancelAllOrders}
            disabled={pendingOrders().length === 0}
            style={{ 
              padding: '8px 16px',
              borderRadius: '6px',
              border: 'none',
              background: pendingOrders().length === 0 ?#6b7280: '#ef4444',
              color: 'white',
              cursor: pendingOrders().length === 0 ?not-allowed: 'pointer',
              fontWeight: '500',
            }}
          >
            全部撤销
          </button>
        </div>
      </div>

      {/* 订单表格 */}
      <div style={{ marginTop: '16px' }}>
        <div style={{ 
          maxHeight: '400px',
          overflowY: 'auto',
          border: `1px solid ${props.theme ==='dark' ?#374151: '#ebeef5'}`,'
          borderRadius: '6px',
        }}>
          {/* 表格头部 */}
          <div style={{ 
            display: 'grid',
            gridTemplateColumns: '120px 100px 60px 80px 80px 80px 80px 140px 120px',
            alignItems: 'center',
            padding: '12px 16px',
            background: props.theme ==='dark' ?#374151: '#f5f7fa',
           'border-bottom: `1px solid ${props.theme ==='dark' ?#4b5563: '#ebeef5'}`,'
            fontWeight: '600',
            fontSize: '14px',
            color: props.theme ==='dark' ?'#d1d5db: '#606266',
          }}>
            <div>订单号</div>
            <div>品种</div>
            <div>方向</div>
            <div>类型</div>
            <div>价格</div>
            <div>数量</div>
            <div>已成交</div>
            <div>状态</div>
            <div>委托时间</div>
            <div>操作</div>
          </div>

          <Show when={currentOrders().length > 0} fallback={
            <div style={{ 
              padding: '32px',
              textAlign: 'center',
              color: props.theme ==='dark' ?#9ca3af: '#6b7280',
            }}>
              暂无订单
            </div>
          }>
            <For each={currentOrders()}>
              {(order) => (
                <div style={{ 
                  display: 'grid',
                  gridTemplateColumns: '120px 100px 60px 80px 80px 80px 80px 140px 120px',
                  alignItems: 'center',
                  padding: '12px 16px',
                 'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#f3f4f6'}`,'
                  fontSize: '14px',
                }}>
                  <div style={{ color: props.theme ==='dark' ?'#f9fafb: '#303133' }}>
                    {order.orderNo}
                  </div>
'
                  <div style={{ color: props.theme ==='dark' ?'#f9fafb: '#303133' }}>
                    {order.symbol}
                  </div>

                  <div>
                    <span style={{ 
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      background: order.direction ==='buy' ?'#f0f9ff: '#fef2f2',
                      color: order.direction ==='buy' ?#059669: '#dc2626',
                    }}>
                      {order.direction ==='buy' ?买入: '卖出'}
                    </span>
                  </div>
'
                  <div style={{ color: props.theme ==='dark' ?'#d1d5db: '#606266' }}>
                    {getOrderTypeText(order.orderType)}
                  </div>

                  <div style={{ 
                    textAlign: 'right',
                    color: props.theme ==='dark' ?'#d1d5db: '#606266',
                  }}>
                    {order.orderType ==='market' ?'市价' : formatPrice(order.price)}
                  </div>

                  <div style={{ 
                    textAlign: 'right',
                    color: props.theme ==='dark' ?'#d1d5db: '#606266',
                  }}>
                    {order.quantity}
                  </div>

                  <div style={{ 
                    textAlign: 'right',
                    color: props.theme ==='dark' ?'#d1d5db: '#606266',
                  }}>
                    {order.filledQuantity}
                  </div>

                  <div>
                    <span style={{ 
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      background: getOrderStatusColor(order.status) +'20',
                      color: getOrderStatusColor(order.status),
                    }}>
                      {getOrderStatusText(order.status)}
                    </span>
                  </div>

                  <div style={{ 
                    fontSize: '12px',
                    color: props.theme ==='dark' ?#9ca3af: '#909399',
                  }}>
                    {formatTime(order.createTime)}
                  </div>
'
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <Show when={order.status ==='pending'}>
                      <button
                        onClick={() => handleModifyOrder(order)}
                        style={{ 
                          padding: '4px 8px',
                          borderRadius: '4px',
                          border: 'none',
                          background: '#3b82f6',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '12px',
                        }}
                      >
                        修改
                      </button>
                      <button
                        onClick={() => handleCancelOrder(order.id)}
                        style={{ 
                          padding: '4px 8px',
                          borderRadius: '4px',
                          border: 'none',
                          background: '#ef4444',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '12px',
                        }}
                      >
                        撤销
                      </button>
                    </Show>
                    <Show when={order.status ==='filled'}>
                      <button
                        onClick={() => handleViewOrderDetail(order)}
                        style={{ 
                          padding: '4px 8px',
                          borderRadius: '4px',
                          border: 'none',
                          background: '#6b7280',
                          color: 'white',
                          cursor: 'pointer',
                          fontSize: '12px',
                        }}
                      >
                        详情
                      </button>
                    </Show>
                  </div>
                </div>
              )}
            </For>
          </Show>
        </div>
      </div>

      {/* 修改订单对话框 */}
      <Show when={modifyDialogVisible()}>
        <div style={{ 
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: '1000',
        }}>
          <div style={{'
            background: props.theme ==='dark' ?#1f2937: '#ffffff',
            borderRadius: '8px',
            padding: '24px',
            width: '400px',
            maxWidth: '90vw',
          }}>
            <h3 style={{ 
              margin: '0 0 20px 0',
              fontSize: '18px',
              fontWeight: 'bold',
              color: props.theme ==='dark' ?'#f9fafb: '#111827',
            }}>
              修改订单
            </h3>
'
            <div style={{ display: 'grid', gap: '16px' }}>
              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  订单号
                </label>
                <input
                  type='text'
                  value={modifyForm().orderNo}
                  disabled
                  style={{'
                    width: `100%,
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#374151: '#f9fafb',
                    color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                  }}
                />
              </div>

              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  品种
                </label>
                <input
                  type='text'
                  value={modifyForm().symbol}
                  disabled
                  style={{'
                    width: `100%,
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#374151: '#f9fafb',
                    color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                  }}
                />
              </div>

              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  价格 *
                </label>
                <input
                  type='number'
                  step='0.01'
                  min='0'
                  value={modifyForm().price}
                  onInput={(e) => setModifyForm(prev => ({ ...prev, price: parseFloat(e.currentTarget.value) || 0 }))}
                  style={{'
                    width: `100%,
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#111827: '#ffffff',
                    color: props.theme ==='dark' ?'#f9fafb: '#374151',
                  }}
                />
              </div>

              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  数量 *
                </label>
                <input
                  type='number'
                  step='100'
                  min='100'
                  value={modifyForm().quantity}
                  onInput={(e) => setModifyForm(prev => ({ ...prev, quantity: parseInt(e.currentTarget.value) || 0 }))}
                  style={{'
                    width: `100%,
                    padding: '8px 12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#111827: '#ffffff',
                    color: props.theme ==='dark' ?'#f9fafb: '#374151',
                  }}
                />
              </div>
            </div>

            <div style={{ 
              display: 'flex',
              gap: '12px',
              marginTop: '24px',
              justifyContent: 'flex-end',
            }}>
              <button
                onClick={() => setModifyDialogVisible(false)}
                style={{ 
                  padding: '8px 16px',
                  borderRadius: '6px',
                  border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                  background: props.theme ==='dark' ?#374151: '#f9fafb',
                  color: props.theme ==='dark' ?'#f9fafb: '#374151',
                  cursor: 'pointer',
                }}
              >
                取消
              </button>
              <button
                onClick={handleConfirmModify}
                disabled={modifying()}
                style={{ 
                  padding: '8px 16px',
                  borderRadius: '6px',
                  border: 'none',
                  background: modifying() ?#6b7280: '#3b82f6',
                  color: 'white',
                  cursor: modifying() ?not-allowed: 'pointer',
                }}
              >
                {modifying() ?修改中...: '确认修改'}
              </button>
            </div>
          </div>
        </div>
      </Show>

      {/* 订单详情对话框 */}
      <Show when={detailDialogVisible() && selectedOrder()}>
        <div style={{ 
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          background: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: '1000',
        }}>
          <div style={{'
            background: props.theme ==='dark' ?#1f2937: '#ffffff',
            borderRadius: '8px',
            padding: '24px',
            width: '600px',
            maxWidth: '90vw',
            maxHeight: '80vh',
            overflowY: 'auto',
          }}>
            <h3 style={{ 
              margin: '0 0 20px 0',
              fontSize: '18px',
              fontWeight: 'bold',
              color: props.theme ==='dark' ?'#f9fafb: '#111827',
            }}>
              订单详情
            </h3>
'
            <div style={{ padding: '16px 0' }}>
              {/* 基本信息 */}
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ 
                  margin: '0 0 12px 0',
                  fontSize: '16px',
                  color: props.theme ==='dark' ?'#f9fafb: '#303133',
                 'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#ebeef5'}`,'
                  paddingBottom: '8px',
                }}>
                  基本信息
                </h4>
                <div style={{ 
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '12px',
                }}>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>订单号' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{selectedOrder()!.orderNo}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>品种' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{selectedOrder()!.symbol}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>方向' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{selectedOrder()!.direction ==='buy' ?买入: '卖出'}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>类型' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{getOrderTypeText(selectedOrder()!.orderType)}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>委托价格' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{formatPrice(selectedOrder()!.price)}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>委托数量' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{selectedOrder()!.quantity}</span>
                  </div>
                </div>
              </div>

              {/* 成交信息 */}
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ 
                  margin: '0 0 12px 0',
                  fontSize: '16px',
                  color: props.theme ==='dark' ?'#f9fafb: '#303133',
                 'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#ebeef5'}`,'
                  paddingBottom: '8px',
                }}>
                  成交信息
                </h4>
                <div style={{ 
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '12px',
                }}>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>已成交数量' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{selectedOrder()!.filledQuantity}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>平均成交价' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133,'
                    }}>{selectedOrder()!.averagePrice > 0 ? formatPrice(selectedOrder()!.averagePrice): '--'}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>成交金额' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133,'
                    }}>{selectedOrder()!.filledAmount ? formatPrice(selectedOrder()!.filledAmount): '--'}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>手续费' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133,'
                    }}>{selectedOrder()!.commission > 0 ? formatPrice(selectedOrder()!.commission): '--'}</span>
                  </div>
                </div>
              </div>

              {/* 时间信息 */}
              <div>
                <h4 style={{ 
                  margin: '0 0 12px 0',
                  fontSize: '16px',
                  color: props.theme ==='dark' ?'#f9fafb: '#303133',
                 'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#ebeef5'}`,'
                  paddingBottom: '8px',
                }}>
                  时间信息
                </h4>
                <div style={{ 
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: '12px',
                }}>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>委托时间' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{formatTime(selectedOrder()!.createTime)}</span>
                  </div>
                  <div style={{ 
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                  }}>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?#9ca3af: '#606266',
                      fontWeight: '500',
                    }}>更新时间' :</span>
                    <span style={{ 
                      fontSize: '14px',
                      color: props.theme ==='dark' ?'#f9fafb: '#303133',
                    }}>{formatTime(selectedOrder()!.updateTime)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div style={{ 
              display: 'flex',
              justifyContent: 'flex-end',
              marginTop: '24px',
            }}>
              <button
                onClick={() => setDetailDialogVisible(false)}
                style={{ 
                  padding: '8px 16px',
                  borderRadius: '6px',
                  border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                  background: props.theme ==='dark' ?#374151: '#f9fafb',
                  color: props.theme ==='dark' ?'#f9fafb: '#374151',
                  cursor: 'pointer',
                }}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </Show>

      {/* 持仓管理 - 保持原有功能 */}
      <Show when={false}>
        <div style={{ padding: '16px' }}>
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            <Show when={props.positions && props.positions.length > 0} fallback={
              <div style={{ 
                padding: '32px',
                textAlign: 'center',
                color: props.theme ==='dark' ?#9ca3af: '#6b7280',
              }}>
                暂无持仓
              </div>
            }>
              <For each={props.positions}>
                {(position) => (
                  <div style={{ 
                    padding: '16px',
                   'border-bottom: `1px solid ${props.theme ==='dark' ?#374151: '#f3f4f6'}`,'
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr 1fr 1fr 1fr 1fr 100px',
                    alignItems: 'center',
                    gap: '12px',
                  }}>
                    <div>
                      <div style={{ 
                        fontWeight: '600',
                        color: props.theme ==='dark' ?'#f9fafb: '#111827',
                      }}>
                        {position.symbol}
                      </div>
                      <div style={{ 
                        fontSize: '12px',
                        color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                      }}>
                        {position.quantity} 股
                      </div>
                    </div>

                    <div>
                      <div style={{'
                        color: props.theme ==='dark' ?'#d1d5db: '#374151',
                      }}>
                        ¥{formatPrice(position.averageCost)}
                      </div>
                      <div style={{ 
                        fontSize: '12px',
                        color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                      }}>
                        成本价
                      </div>
                    </div>

                    <div>
                      <div style={{'
                        color: props.theme ==='dark' ?'#d1d5db: '#374151',
                        fontWeight: '500',
                      }}>
                        ¥{formatPrice(position.currentPrice)}
                      </div>
                      <div style={{ 
                        fontSize: '12px',
                        color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                      }}>
                        现价
                      </div>
                    </div>

                    <div>
                      <div style={{
                        color: getPnLColor(position.unrealizedPnL),'
                        fontWeight: '500',
                      }}>
                        ¥{formatPrice(Math.abs(position.unrealizedPnL))}
                      </div>
                      <div style={{ 
                        fontSize: '12px',
                        color: getPnLColor(position.unrealizedPnL),
                      }}>
                        {formatPercent((position.currentPrice - position.averageCost) / position.averageCost * 100)}
                      </div>
                    </div>

                    <div>
                      <div style={{'
                        color: props.theme ==='dark' ?'#d1d5db: '#374151',
                        fontWeight: '500',
                      }}>
                        ¥{formatPrice(position.totalValue)}
                      </div>
                      <div style={{ 
                        fontSize: '12px',
                        color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                      }}>
                        市值
                      </div>
                    </div>

                    <div>
                      <div style={{ 
                        fontSize: '12px',
                        color: props.theme ==='dark' ?#9ca3af: '#6b7280',
                      }}>
                        {format(position.lastUpdate,'MM-dd HH:mm', { locale: zhCN })}
                      </div>
                    </div>

                    <div>
                      <Show when={props.tradingEnabled}>
                        <button
                          onClick={() => handleClosePosition(position.symbol)}
                          style={{ 
                            padding: '4px 8px',
                            borderRadius: '4px',
                            border: 'none',
                            background: '#ef4444',
                            color: 'white',
                            cursor: 'pointer',
                            fontSize: '12px',
                          }}
                        >
                          平仓
                        </button>
                      </Show>
                    </div>
                  </div>
                )}
              </For>
            </Show>
          </div>
        </div>
      </Show>

      {/* 下单交易 */}
      <Show when={activeTab() ==='create' && props.tradingEnabled}>
        <div style={{ padding: '24px' }}>
          <div style={{ 
            maxWidth: '500px',
            margin: '0 auto',
          }}>
            <h3 style={{ 
              margin: '0 0 24px 0',
              fontSize: '18px',
              fontWeight: 'bold',
              color: props.theme ==='dark' ?'#f9fafb: '#111827',
              textAlign: 'center',
            }}>
              创建新订单
            </h3>
'
            <div style={{ display: 'grid', gap: '16px' }}>
              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  股票代码 *
                </label>
                <input
                  type='text'
                  placeholder='例如: 000001.SZ'
                  value={newOrder().symbol}
                  onInput={(e) => setNewOrder(prev => ({ ...prev, symbol: e.currentTarget.value.toUpperCase() }))}
                  style={{'
                    width: `100%,
                    padding: '12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#111827: '#ffffff',
                    color: props.theme ==='dark' ?'#f9fafb: '#374151',
                  }}
                />
              </div>
'
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <label style={{ 
                    display: 'block',
                    fontWeight: '500',
                    marginBottom: '6px',
                    color: props.theme ==='dark' ?'#d1d5db: '#374151',
                  }}>
                    交易方向 *
                  </label>
                  <select
                    value={newOrder().type}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, type: e.currentTarget.value as any }))}
                    style={{'
                      width: `100%,
                      padding: '12px',
                      borderRadius: '6px',
                      border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                      background: props.theme ==='dark' ?#111827: '#ffffff',
                      color: props.theme ==='dark' ?'#f9fafb: '#374151',
                    }}
                  >
                    <option value="buy">买入</option>
                    <option value="sell">卖出</option>
                  </select>
                </div>

                <div>
                  <label style={{ 
                    display: 'block',
                    fontWeight: '500',
                    marginBottom: '6px',
                    color: props.theme ==='dark' ?'#d1d5db: '#374151',
                  }}>
                    订单类型 *
                  </label>
                  <select
                    value={newOrder().orderType}
                    onChange={(e) => setNewOrder(prev => ({ ...prev, orderType: e.currentTarget.value as any }))}
                    style={{'
                      width: `100%,
                      padding: '12px',
                      borderRadius: '6px',
                      border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                      background: props.theme ==='dark' ?#111827: '#ffffff',
                      color: props.theme ==='dark' ?'#f9fafb: '#374151',
                    }}
                  >
                    <option value="market">市价单</option>
                    <option value="limit">限价单</option>
                    <option value="stop'>止损单</option>
                  </select>
                </div>
              </div>
'
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <label style={{ 
                    display: 'block',
                    fontWeight: '500',
                    marginBottom: '6px',
                    color: props.theme ==='dark' ?'#d1d5db: '#374151',
                  }}>
                    数量 (股) *
                  </label>
                  <input
                    type='number'
                    min='0'
                    step='100'
                    value={newOrder().quantity}
                    onInput={(e) => setNewOrder(prev => ({ ...prev, quantity: parseInt(e.currentTarget.value) || 0 }))}
                    style={{'
                      width: `100%,
                      padding: '12px',
                      borderRadius: '6px',
                      border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                      background: props.theme ==='dark' ?#111827: '#ffffff',
                      color: props.theme ==='dark' ?'#f9fafb: '#374151',
                    }}
                  />
                </div>
'
                <Show when={newOrder().orderType ==='limit' || newOrder().orderType ==='stop'}>
                  <div>
                    <label style={{ 
                      display: 'block',
                      fontWeight: '500',
                      marginBottom: '6px',
                      color: props.theme ==='dark' ?'#d1d5db: '#374151',
                    }}>
                      {newOrder().orderType ==='limit' ?限价: '止损价'} (元) *
                    </label>
                    <input
                      type='number'
                      min='0'
                      step= '0.01'
                      value={newOrder().orderType ==='limit' ? newOrder().price: newOrder().stopPrice}
                      onInput={(e) => {
                        const value = parseFloat(e.currentTarget.value) || 0;
                        if (newOrder().orderType ==='limit') {
                          setNewOrder(prev => ({ ...prev, price: value }));
                        } else {
                          setNewOrder(prev => ({ ...prev, stopPrice: value }));
                        }
                      }}
                      style={{'
                        width: `100%,
                        padding: '12px',
                        borderRadius: '6px',
                        border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                        background: props.theme ==='dark' ?#111827: '#ffffff',
                        color: props.theme ==='dark' ?'#f9fafb: '#374151',
                      }}
                    />
                  </div>
                </Show>
              </div>

              <div>
                <label style={{ 
                  display: 'block',
                  fontWeight: '500',
                  marginBottom: '6px',
                  color: props.theme ==='dark' ?'#d1d5db: '#374151',
                }}>
                  备注
                </label>
                <textarea
                  placeholder='订单备注信息...'
                  value={newOrder().notes}
                  onInput={(e) => setNewOrder(prev => ({ ...prev, notes: e.currentTarget.value }))}
                  rows='3'
                  style={{'
                    width: `100%,
                    padding: '12px',
                    borderRadius: '6px',
                    border: `1px solid ${props.theme ==='dark' ?#4b5563: '#d1d5db'}`,'
                    background: props.theme ==='dark' ?#111827: '#ffffff',
                    color: props.theme ==='dark' ?'#f9fafb: '#374151',
                    resize: 'vertical',
                  }}
                />
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={isSubmitting() || !newOrder().symbol || newOrder().quantity <= 0}
                style={{'
                  width: `100%,
                  padding: '12px',
                  borderRadius: '6px',
                  border: 'none',
                  background: isSubmitting() ?'#6b7280': newOrder().type ==='buy' ?#10b981: '#ef4444',
                  color: 'white',
                  cursor: isSubmitting() ?not-allowed: 'pointer',
                  fontWeight: '500',
                  fontSize: '16px',
                }}
              >
                {isSubmitting() ?'提交中...' : `${newOrder().type ==='buy' ?买入: '卖出'} ${newOrder().symbol}`}
              </button>
            </div>
          </div>
        </div>
      </Show>
    </div>
  );
}