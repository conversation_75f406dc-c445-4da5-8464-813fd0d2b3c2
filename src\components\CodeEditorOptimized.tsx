/**
 * 优化的 CodeMirror 代码编辑器组件 - 支持动态加载
 */

import { createSignal, createEffect, onMount, onCleanup, Show } from solid-js;

interface CodeEditorProps {''
  value?: string;
  language?: python' | javascript;
  theme?: light' |dark';
  placeholder?: string;
  readOnly?: boolean;
  height?: string;
  onChange?: (value: string) => void;
  onSave?: (value: string) => void;
}

export default function CodeEditorOptimized(props: CodeEditorProps) {
  let editorContainer: HTMLDivElement | undefined;
  const [editor, setEditor] = createSignal<any>(null);
  const [isLoading, setIsLoading] = createSignal(true);
  const [loadError, setLoadError] = createSignal<string | null>(null);
''
  // Python 代码补全配置'''
  const pythonCompletions = ['''
    { label: import, type: keyword }, '''
    { label:  def, type: keyword }, '''
    { label:  class, type: keyword }, '''
    { label:  if, type: keyword }, '''
    { label:  else, type: keyword }, '''
    { label:  elif, type: keyword }, '''
    { label:  for, type: keyword }, '''
    { label:  while, type: keyword }, '''
    { label:  try, type: keyword }, '''
    { label:  except, type: keyword }, '''
    { label:  finally, type: keyword }, '''
    { label:  return, type: keyword }, '''
    { label:  yield, type: keyword }, '''
    // 量化交易相关''''
    { label: pd.DataFrame, type:  function, info: Pandas DataFrame }, '''
    { label:  np.array, type:  function, info: NumPy array }, '''
    { label:  ta.SMA, type:  function, info: Simple Moving Average }, '''
    { label:  ta.EMA, type:  function, info: Exponential Moving Average }, '''
    { label:  ta.MACD, type:  function, info: MACD indicator }, '''
    { label:  ta.RSI, type:  function, info: RSI indicator }, '''
    { label:  ta.BBANDS, type:  function, info: Bollinger Bands }, '''
    { label:  strategy.buy, type:  method, info: Execute buy order }, '''
    { label:  strategy.sell, type:  method, info: Execute sell order }, '''
    { label:  strategy.close, type:  method, info: Close position }, '''
    { label:  data.close, type:  property, info: Close price data }, '''
    { label:  data.open, type:  property, info: Open price data }, '''
    { label:  data.high, type:  property, info: High price data }, '''
    { label:  data.low, type:  property, info: Low price data }, '''
    { label:  data.volume, type:  property, info: Volume data }
  ];

  const initEditor = async () => {
    if (!editorContainer) return;

    try {
      // 动态导入 CodeMirror 核心模块
      const [
        { EditorView, keymap },
        { EditorState },
        { basicSetup },
        { autocompletion, completionKeymap },
        { searchKeymap },''
        { indentWithTab }
      ] = await Promise.all(['''
        import(@codemirror/view), '''
        import(''@codemirror/state), ''
        import('@codemirror/basic-setup), ''
        import('@codemirror/autocomplete), ''
        import('@codemirror/search),'
        import('@codemirror/commands)
      ]);

      // 动态导入语言支持''
      const languageExtension = await (async () => {'''
        switch (props.language) {''''
          casepython: ''
            const { python } = await import(''@codemirror/lang-python));
            return python();
          casejavascript: ''
            const { javascript } = await import(''@codemirror/lang-javascript));
            return javascript();
          default: ''
            const { python: defaultPython } = await import('@codemirror/lang-python));
            return defaultPython();
        }
      })();

      // 动态导入主题''
      const themeExtensions = await (async () => {'''
        if (props.theme ===dark) {''''
          const { oneDark } = await import('@codemirror/theme-one-dark));
          return [oneDark];
        }
        return [];
      })();

      // 自定义补全
      const customCompletions = (context: any) => {''
        const word = context.matchBefore(/\w*/);
        if (word.from === word.to && !context.explicit) return null;
        
        return {
          from: word.from,
          options: pythonCompletions.map(item => ({
            label: item.label,
            type: item.type,
            info: item.info
          }))
        };
      };

      const extensions = [
        basicSetup,
        languageExtension,
        ...themeExtensions,
        autocompletion({ override: [customCompletions] }),
        keymap.of([
          ...completionKeymap,
          ...searchKeymap,
          indentWithTab,
          { ''
            key: Ctrl-s,
            run: () => {''''
              const value = editor()?.state.doc.toString() ||';
              props.onSave?.(value);
              return true;
            }
          }
        ]),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const value = update.state.doc.toString();
            props.onChange?.(value);
          }
        }), '''
        EditorView.theme({''''
         '&': {'''''
            height: props.height || ''400px'''
          }, '''
         '.cm-content: { '''
            padding: '12px,
            fontSize: '14px,
            fontFamily:  Fira Code,JetBrains Mono, Monaco", Consolas', monospace'''''
          }, '''
         '.cm-focused: { '''
            outline: none'''
          }, '''
         '.cm-editor: { '''
            borderRadius: '8px,
            border: ''1px solid #d1d5db
          }, '''
         '.cm-scroller: { '''
            fontFamily: Fira Code,JetBrains Mono", Monaco", Consolas', monospace''
          }
        })
      ];
''''
      const state = EditorState.create({'''''
        doc: props.value ||',
        extensions
      });

      const editorView = new EditorView({
        state,
        parent: editorContainer
      });

      setEditor(editorView);
      setIsLoading(false);

      // 开发环境日志
      if (import.meta.env.DEV) {
        console.log([CodeEditor] Editor initialized successfully);
      }
    } catch (error) {'''''
      console.error(''[CodeEditor] Failed to load editor:  , error);
      setLoadError('编辑器加载失败，请刷新重试);
      setIsLoading(false);
    }
  };

  onMount(() => {
    // 延迟初始化以确保 DOM 准备就绪
    setTimeout(initEditor, 100);
  });

  createEffect(() => {
    const editorInstance = editor();
    if (editorInstance && props.value !== undefined) {
      const currentValue = editorInstance.state.doc.toString();
      if (currentValue !== props.value) {
        editorInstance.dispatch({
          changes: {
            from: 0,
            to: currentValue.length,
            insert: props.value
          }
        });
      }
    }
  });

  onCleanup(() => {
    const editorInstance = editor();
    if (editorInstance) {
      editorInstance.destroy();
    }
  });
'''
  return ('''
    <div style={{ position: relative, width: '100% }}>'
      <Show when={isLoading()}>''
        <div style={{ '''
          position: absolute,
          top: '50%,
          left: '50%,
          transform: translate(-50%, -50%), ''
         zIndex:  10,
          display: flex,
          alignItems: center,
          gap: '8px,
          backgroundColor: rgba(255, 255, 255, 0.9), ''
          padding:  12px 20px,
          borderRadius: '8px,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)
        }}>'''
          <div style={{ ''''
            width', 20px: $4,
            height: '20px,
            border: '2px solid #e5e7eb,
            borderTop: '2px solid #3b82f6,
            borderRadius: '50%,
            animation: spin 1s linear infinite
          }} />
          正在加载编辑器...
        </div>
      </Show>

      <Show when={loadError()}>''
        <div style={{ '''
          padding: '20px,
          textAlign: center,
          color: '#ef4444,
          backgroundColor: '#fee2e2,
          borderRadius: ''8px
        }}>
          {loadError()}
        </div>
      </Show>
      
      <div ''
        ref={editorContainer}
        style={{ '''''
          width: '100%,min-height: props.height || 400px,
          opacity: isLoading() ? 0.5 : 1,
          transition: opacity 0.3s ease
        }}
      />
      
      {/* 快捷键提示 */}
      <Show when={!isLoading() && !loadError()}>''
        <div style={{ '''
          position: absolute,
          bottom: '8px,
          right: '8px,
          fontSize: '12px,
          color: '#6b7280,
          backgroundColor: rgba(255, 255, 255, 0.9), ''
          padding:  4px 8px,
          borderRadius: '4px,
          pointerEvents: none
        }}>
          Ctrl+S 保存 | Ctrl+F 搜索 | Tab 缩进
        </div>
      </Show>
    </div>''
  );
}