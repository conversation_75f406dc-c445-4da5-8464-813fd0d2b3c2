/**
 * 回测分析API
 */
import { httpClient } from '../utils/http'
import { API_PATHS, ENV_CONFIG, BACKTEST_CONFIG } from '../utils/constants'
import type { ApiResponse, PaginatedResponse } from '../utils/http'
// 回测状态类型
export type BacktestStatus = pending | 'running' | 'completed' | 'failed' | 'cancelled'
// 回测配置
export interface BacktestConfig {
  strategyId: string
  symbol: string
  startDate: string
  endDate: string
  initialCapital: number
  commission: number
  slippage: number
  benchmark?: string
  parameters?: Record<string, any>
}

// 回测结果
export interface BacktestResult {
  id: string
  strategyId: string
  strategyName: string
  symbol: string
  status: BacktestStatus
  config: BacktestConfig
  
  // 性能指标
  totalReturn: number
  annualizedReturn: number
  maxDrawdown: number
  sharpeRatio: number
  sortinoRatio: number
  calmarRatio: number
  winRate: number
  profitFactor: number
  
  // 交易统计
  totalTrades: number
  winningTrades: number
  losingTrades: number
  avgWinningTrade: number
  avgLosingTrade: number
  maxWinningTrade: number
  maxLosingTrade: number
  avgTradeDuration: number
  
  // 风险指标
  volatility: number
  beta: number
  alpha: number
  informationRatio: number
  trackingError: number
  
  // 时间信息
  startTime: string
  endTime?: string
  duration?: number
  createdAt: string
  updatedAt: string
  
  // 详细数据
  equityCurve?: Array<{ date: string; value: number }>
  trades?: BacktestTrade[]
  dailyReturns?: Array<{ date: string; return: number }>
  drawdownCurve?: Array<{ date: string; drawdown: number }>
  
  // 错误信息
  error?: string
  logs?: string[]
}

// 回测交易记录
export interface BacktestTrade {
  id: string
  symbol: string
  side: buy | 'sell'
  quantity: number
  price: number
  amount: number
  commission: number
  timestamp: string
  pnl?: number
  pnlPercent?: number
  holdingPeriod?: number
}

// 回测任务
export interface BacktestTask {
  id: string
  name: string
  strategyId: string
  strategyName: string
  symbol: string
  status: BacktestStatus
  progress: number
  config: BacktestConfig
  createdAt: string
  startTime?: string
  endTime?: string
  error?: string
}

// 请求参数类型
export interface BacktestListParams {
  page?: number
  pageSize?: number
  strategyId?: string
  status?: BacktestStatus
  symbol?: string
  sortBy?: string
  sortOrder?: asc | 'desc'
}

export interface CreateBacktestRequest {
  name: string
  strategyId: string
  symbol: string
  startDate: string
  endDate: string
  initialCapital?: number
  commission?: number
  slippage?: number
  benchmark?: string
  parameters?: Record<string, any>
}

// 模拟数据生成
const generateMockBacktestResult = (id: string): BacktestResult => {
  const totalReturn = (Math.random() - 0.3) * 50
  const volatility = Math.random() * 30 + 5
  const sharpeRatio = totalReturn / volatility
  
  return {
    id,
    strategyId: `strategy-${Math.floor(Math.random() * 100)}`,
    strategyName: `策略${id.slice(-3)}`,
    symbol: 000001,
    status: 'completed',
    config: {
      strategyId: `strategy-${Math.floor(Math.random() * 100)}`,'
      symbol: '000001',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      initialCapital: 100000,
      commission: 0.0003,
      slippage: 0.0001
    },
    
    // 性能指标
    totalReturn: Number(totalReturn.toFixed(2)),
    annualizedReturn: Number((totalReturn * 1.2).toFixed(2)),
    maxDrawdown: Number((Math.random() * 20).toFixed(2)),
    sharpeRatio: Number(sharpeRatio.toFixed(2)),
    sortinoRatio: Number((sharpeRatio * 1.2).toFixed(2)),
    calmarRatio: Number((sharpeRatio * 0.8).toFixed(2)),
    winRate: Number((Math.random() * 40 + 40).toFixed(1)),
    profitFactor: Number((Math.random() * 2 + 0.5).toFixed(2)),
    
    // 交易统计
    totalTrades: Math.floor(Math.random() * 200 + 50),
    winningTrades: Math.floor(Math.random() * 120 + 30),
    losingTrades: Math.floor(Math.random() * 80 + 20),
    avgWinningTrade: Number((Math.random() * 500 + 100).toFixed(2)),
    avgLosingTrade: Number((-Math.random() * 300 - 50).toFixed(2)),
    maxWinningTrade: Number((Math.random() * 2000 + 500).toFixed(2)),
    maxLosingTrade: Number((-Math.random() * 1000 - 200).toFixed(2)),
    avgTradeDuration: Number((Math.random() * 10 + 1).toFixed(1)),
    
    // 风险指标
    volatility: Number(volatility.toFixed(2)),
    beta: Number((Math.random() * 1.5 + 0.5).toFixed(2)),
    alpha: Number((Math.random() * 10 - 5).toFixed(2)),
    informationRatio: Number((Math.random() * 2).toFixed(2)),
    trackingError: Number((Math.random() * 5).toFixed(2)),
    
    // 时间信息
    startTime: 2023-01-01T00:00:00Z,
    endTime: '2024-01-01T00:00:00Z',
    duration: 365 * 24 * 60 * 60 * 1000,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

const generateMockBacktestTask = (id: string): BacktestTask => {'
  const statuses: BacktestStatus[] = ['pending', 'running', 'completed', 'failed']
  const status = statuses[Math.floor(Math.random() * statuses.length)]
  
  return {
    id,
    name: `回测任务${id.slice(-3)}`,
    strategyId: `strategy-${Math.floor(Math.random() * 100)}`,
    strategyName: `策略${id.slice(-3)}`,'
    symbol: '000001',
    status,'
    progress: status === 'completed' ? 100 : Math.floor(Math.random() * 100),
    config: {
      strategyId: `strategy-${Math.floor(Math.random() * 100)}`,'
      symbol: '000001',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      initialCapital: 100000,
      commission: 0.0003,
      slippage: 0.0001
    },
    createdAt: new Date().toISOString(),'
    startTime: status !== 'pending' ? new Date().toISOString() : undefined,'
    endTime: status === 'completed' ? new Date().toISOString() : undefined
  }
}

/**
 * 回测分析API类
 */
export class BacktestAPI {
  private useMock = ENV_CONFIG.enableMock

  /**
   * 获取回测列表
   */
  async getBacktests(params?: BacktestListParams): Promise<PaginatedResponse<BacktestTask>> {
    if (this.useMock) {
      const mockBacktests = Array.from({ length: 15 }, (_, i) => '
        generateMockBacktestTask(`backtest-${String(i + 1).padStart(3, '0')}`)
      )
      
      let filteredBacktests = mockBacktests
      
      // 应用过滤条件
      if (params?.strategyId) {
        filteredBacktests = filteredBacktests.filter(b => b.strategyId === params.strategyId)
      }
      
      if (params?.status) {
        filteredBacktests = filteredBacktests.filter(b => b.status === params.status)
      }
      
      if (params?.symbol) {
        filteredBacktests = filteredBacktests.filter(b => b.symbol === params.symbol)
      }
      
      // 分页
      const page = params?.page || 1
      const pageSize = params?.pageSize || 10
      const start = (page - 1) * pageSize
      const end = start + pageSize
      
      return {
        items: filteredBacktests.slice(start, end),
        total: filteredBacktests.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredBacktests.length / pageSize)
      }
    }

    try {
      const response = await httpClient.get<PaginatedResponse<BacktestTask>>(API_PATHS.BACKTEST.HISTORY, params)
      return response.data!
    } catch (error) {
      console.warn(获取回测列表API调用失败，使用模拟数据:, error)
      return {'
        items: [generateMockBacktestTask('backtest-001')],
        total: 1,
        page: 1,
        pageSize: 10,
        totalPages: 1
      }
    }
  }

  /**
   * 获取回测结果详情
   */
  async getBacktestResult(id: string): Promise<BacktestResult> {
    if (this.useMock) {
      return generateMockBacktestResult(id)
    }

    try {
      const response = await httpClient.get<BacktestResult>(`${API_PATHS.BACKTEST.RESULT}/${id}`)
      return response.data!
    } catch (error) {'
      console.warn('获取回测结果API调用失败，使用模拟数据:', error)
      return generateMockBacktestResult(id)
    }
  }

  /**
   * 创建回测任务
   */
  async createBacktest(data: CreateBacktestRequest): Promise<BacktestTask> {
    if (this.useMock) {
      const newTask = generateMockBacktestTask(`backtest-${Date.now()}`)
      return {
        ...newTask,
        name: data.name,
        strategyId: data.strategyId,
        symbol: data.symbol,
        config: {
          ...newTask.config,
          ...data,
          initialCapital: data.initialCapital || BACKTEST_CONFIG.DEFAULTS.INITIAL_CAPITAL,
          commission: data.commission || BACKTEST_CONFIG.DEFAULTS.COMMISSION,
          slippage: data.slippage || BACKTEST_CONFIG.DEFAULTS.SLIPPAGE
        }
      }
    }

    try {
      const response = await httpClient.post<BacktestTask>(API_PATHS.BACKTEST.RUN, data)
      return response.data!
    } catch (error) {'
      console.warn('创建回测任务API调用失败:', error)
      throw error
    }
  }

  /**
   * 启动回测
   */
  async startBacktest(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.post(`${API_PATHS.BACKTEST.START}/${id}`)
    } catch (error) {'
      console.warn('启动回测API调用失败:', error)
      throw error
    }
  }

  /**
   * 停止回测
   */
  async stopBacktest(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.post(`${API_PATHS.BACKTEST.STOP}/${id}`)
    } catch (error) {'
      console.warn('停止回测API调用失败:', error)
      throw error
    }
  }

  /**
   * 删除回测
   */
  async deleteBacktest(id: string): Promise<void> {
    if (this.useMock) {
      return Promise.resolve()
    }

    try {
      await httpClient.delete(`${API_PATHS.BACKTEST.DELETE}/${id}`)
    } catch (error) {'
      console.warn('删除回测API调用失败:', error)
      throw error
    }
  }

  /**
   * 获取回测健康状态
   */
  async getHealth(): Promise<{ status: string; message: string }> {
    if (this.useMock) {
      return { 
        status: 'healthy',
        message: '回测服务运行正常'
      }
    }

    try {
      const response = await httpClient.get<{ status: string; message: string }>(API_PATHS.BACKTEST.HEALTH)
      return response.data!
    } catch (error) {'
      console.warn('获取回测健康状态API调用失败:', error)
      return { 
        status: 'error',
        message: '回测服务连接失败'
      }
    }
  }
}

// 创建实例
export const backtestApi = new BacktestAPI()

// 导出默认实例
export default backtestApi
