/**
 * 回测指标计算工具
 * 提供专业的量化交易回测指标计算
 */

export interface BacktestMetrics {
  // 基础收益指标
  totalReturn: number;
  annualizedReturn: number;
  cumulativeReturn: number;
  
  // 风险指标
  volatility: number;
  maxDrawdown: number;
  maxDrawdownDuration: number;
  
  // 风险调整收益指标
  sharpeRatio: number;
  calmarRatio: number;
  sortinoRatio: number;
  informationRatio: number;
  
  // 交易统计
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  
  // 风险价值
  var95: number;
  var99: number;
  cvar95: number;
  cvar99: number;
  
  // 其他指标
  beta: number;
  alpha: number;
  trackingError: number;
  treynorRatio: number;
}

export interface EquityPoint {
  timestamp: string;
  equity: number;
  drawdown: number;
}

export interface Trade {
  timestamp: string;
  symbol: string;
  side: buy | sell';
  quantity: number;
  price: number;
  pnl?: number;
}

/**
 * 计算回测指标
 */
export function calculateBacktestMetrics(
  equity: EquityPoint[],
  trades: Trade[],
  benchmarkReturns?: number[],
  riskFreeRate: number = 0.03
): BacktestMetrics {
  if (equity.length === 0) {
    throw new Error('权益数据不能为空');
  }

  // 计算日收益率
  const returns = calculateReturns(equity);
  
  // 基础收益指标
  const totalReturn = calculateTotalReturn(equity);
  const annualizedReturn = calculateAnnualizedReturn(returns);
  const cumulativeReturn = totalReturn;
  
  // 风险指标
  const volatility = calculateVolatility(returns);
  const { maxDrawdown, maxDrawdownDuration } = calculateMaxDrawdown(equity);
  
  // 风险调整收益指标
  const sharpeRatio = calculateSharpeRatio(returns, riskFreeRate);
  const calmarRatio = calculateCalmarRatio(annualizedReturn, maxDrawdown);
  const sortinoRatio = calculateSortinoRatio(returns, riskFreeRate);
  const informationRatio = benchmarkReturns ? 
    calculateInformationRatio(returns, benchmarkReturns) : 0;
  
  // 交易统计
  const tradeStats = calculateTradeStatistics(trades);
  
  // 风险价值
  const varMetrics = calculateVaR(returns);
  
  // 其他指标
  const beta = benchmarkReturns ? calculateBeta(returns, benchmarkReturns) : 0;
  const alpha = benchmarkReturns ? 
    calculateAlpha(annualizedReturn, beta, benchmarkReturns, riskFreeRate) : 0;
  const trackingError = benchmarkReturns ? 
    calculateTrackingError(returns, benchmarkReturns) : 0;
  const treynorRatio = beta !== 0 ? (annualizedReturn - riskFreeRate) / beta : 0;

  return {
    totalReturn,
    annualizedReturn,
    cumulativeReturn,
    volatility,
    maxDrawdown,
    maxDrawdownDuration,
    sharpeRatio,
    calmarRatio,
    sortinoRatio,
    informationRatio,
    ...tradeStats,
    ...varMetrics,
    beta,
    alpha,
    trackingError,
    treynorRatio
  };
}

/**
 * 计算日收益率
 */
function calculateReturns(equity: EquityPoint[]): number[] {
  const returns = [];
  for (let i = 1; i < equity.length; i++) {
    const prevEquity = equity[i - 1].equity;
    const currentEquity = equity[i].equity;
    const dailyReturn = (currentEquity - prevEquity) / prevEquity;
    returns.push(dailyReturn);
  }
  return returns;
}

/**
 * 计算总收益率
 */
function calculateTotalReturn(equity: EquityPoint[]): number {
  if (equity.length === 0) return 0;
  const initialEquity = equity[0].equity;
  const finalEquity = equity[equity.length - 1].equity;
  return (finalEquity - initialEquity) / initialEquity;
}

/**
 * 计算年化收益率
 */
function calculateAnnualizedReturn(returns: number[]): number {
  if (returns.length === 0) return 0;
  const totalReturn = returns.reduce((acc, ret) => acc * (1 + ret), 1) - 1;
  const years = returns.length / 252; // 假设252个交易日为一年
  return Math.pow(1 + totalReturn, 1 / years) - 1;
}

/**
 * 计算波动率（年化）
 */
function calculateVolatility(returns: number[]): number {
  if (returns.length === 0) return 0;
  const mean = returns.reduce((acc, ret) => acc + ret, 0) / returns.length;
  const variance = returns.reduce((acc, ret) => acc + Math.pow(ret - mean, 2), 0) / returns.length;
  return Math.sqrt(variance * 252); // 年化波动率
}

/**
 * 计算最大回撤和最大回撤持续期
 */
function calculateMaxDrawdown(equity: EquityPoint[]): { maxDrawdown: number; maxDrawdownDuration: number } {
  let maxDrawdown = 0;
  let maxDrawdownDuration = 0;
  let peak = equity[0]?.equity || 0;
  let drawdownStart = 0;
  let currentDrawdownDuration = 0;

  for (let i = 0; i < equity.length; i++) {
    const currentEquity = equity[i].equity;
    
    if (currentEquity > peak) {
      peak = currentEquity;
      if (currentDrawdownDuration > 0) {
        maxDrawdownDuration = Math.max(maxDrawdownDuration, currentDrawdownDuration);
        currentDrawdownDuration = 0;
      }
    } else {
      const drawdown = (peak - currentEquity) / peak;
      maxDrawdown = Math.max(maxDrawdown, drawdown);
      
      if (currentDrawdownDuration === 0) {
        drawdownStart = i;
      }
      currentDrawdownDuration = i - drawdownStart + 1;
    }
  }

  // 检查最后一次回撤
  if (currentDrawdownDuration > 0) {
    maxDrawdownDuration = Math.max(maxDrawdownDuration, currentDrawdownDuration);
  }

  return { maxDrawdown, maxDrawdownDuration };
}

/**
 * 计算夏普比率
 */
function calculateSharpeRatio(returns: number[], riskFreeRate: number): number {
  if (returns.length === 0) return 0;
  const excessReturns = returns.map(ret => ret - riskFreeRate / 252);
  const meanExcessReturn = excessReturns.reduce((acc, ret) => acc + ret, 0) / excessReturns.length;
  const volatility = Math.sqrt(
    excessReturns.reduce((acc, ret) => acc + Math.pow(ret - meanExcessReturn, 2), 0) / excessReturns.length
  );
  return volatility > 0 ? (meanExcessReturn * Math.sqrt(252)) / (volatility * Math.sqrt(252)) : 0;
}

/**
 * 计算卡尔马比率
 */
function calculateCalmarRatio(annualizedReturn: number, maxDrawdown: number): number {
  return maxDrawdown > 0 ? annualizedReturn / maxDrawdown : 0;
}

/**
 * 计算索提诺比率
 */
function calculateSortinoRatio(returns: number[], riskFreeRate: number): number {
  if (returns.length === 0) return 0;
  const excessReturns = returns.map(ret => ret - riskFreeRate / 252);
  const meanExcessReturn = excessReturns.reduce((acc, ret) => acc + ret, 0) / excessReturns.length;
  
  // 只考虑负收益的波动率
  const negativeReturns = excessReturns.filter(ret => ret < 0);
  if (negativeReturns.length === 0) return Infinity;
  
  const downwardDeviation = Math.sqrt(
    negativeReturns.reduce((acc, ret) => acc + Math.pow(ret, 2), 0) / returns.length
  );
  
  return downwardDeviation > 0 ? (meanExcessReturn * Math.sqrt(252)) / (downwardDeviation * Math.sqrt(252)) : 0;
}

/**
 * 计算信息比率
 */
function calculateInformationRatio(returns: number[], benchmarkReturns: number[]): number {
  if (returns.length !== benchmarkReturns.length || returns.length === 0) return 0;
  
  const activeReturns = returns.map((ret, i) => ret - benchmarkReturns[i]);
  const meanActiveReturn = activeReturns.reduce((acc, ret) => acc + ret, 0) / activeReturns.length;
  const trackingError = Math.sqrt(
    activeReturns.reduce((acc, ret) => acc + Math.pow(ret - meanActiveReturn, 2), 0) / activeReturns.length
  );
  
  return trackingError > 0 ? meanActiveReturn / trackingError : 0;
}

/**
 * 计算交易统计
 */
function calculateTradeStatistics(trades: Trade[]) {
  const profitableTrades = trades.filter(trade => (trade.pnl || 0) > 0);
  const losingTrades = trades.filter(trade => (trade.pnl || 0) < 0);
  
  const winRate = trades.length > 0 ? profitableTrades.length / trades.length : 0;
  
  const totalProfit = profitableTrades.reduce((acc, trade) => acc + (trade.pnl || 0), 0);
  const totalLoss = Math.abs(losingTrades.reduce((acc, trade) => acc + (trade.pnl || 0), 0));
  const profitFactor = totalLoss > 0 ? totalProfit / totalLoss : 0;
  
  const averageWin = profitableTrades.length > 0 ? 
    totalProfit / profitableTrades.length : 0;
  const averageLoss = losingTrades.length > 0 ? 
    totalLoss / losingTrades.length : 0;
  
  const largestWin = profitableTrades.length > 0 ? 
    Math.max(...profitableTrades.map(trade => trade.pnl || 0)) : 0;
  const largestLoss = losingTrades.length > 0 ? 
    Math.min(...losingTrades.map(trade => trade.pnl || 0)) : 0;

  return {
    winRate,
    profitFactor,
    averageWin,
    averageLoss,
    largestWin,
    largestLoss
  };
}

/**
 * 计算风险价值 (VaR) 和条件风险价值 (CVaR)
 */
function calculateVaR(returns: number[]) {
  if (returns.length === 0) {
    return { var95: 0, var99: 0, cvar95: 0, cvar99: 0 };
  }

  const sortedReturns = [...returns].sort((a, b) => a - b);
  
  // VaR计算
  const var95Index = Math.floor(returns.length * 0.05);
  const var99Index = Math.floor(returns.length * 0.01);
  
  const var95 = sortedReturns[var95Index] || 0;
  const var99 = sortedReturns[var99Index] || 0;
  
  // CVaR计算（尾部期望）
  const cvar95Returns = sortedReturns.slice(0, var95Index + 1);
  const cvar99Returns = sortedReturns.slice(0, var99Index + 1);
  
  const cvar95 = cvar95Returns.length > 0 ? 
    cvar95Returns.reduce((acc, ret) => acc + ret, 0) / cvar95Returns.length : 0;
  const cvar99 = cvar99Returns.length > 0 ? 
    cvar99Returns.reduce((acc, ret) => acc + ret, 0) / cvar99Returns.length : 0;

  return { var95, var99, cvar95, cvar99 };
}

/**
 * 计算Beta系数
 */
function calculateBeta(returns: number[], benchmarkReturns: number[]): number {
  if (returns.length !== benchmarkReturns.length || returns.length === 0) return 0;
  
  const meanReturn = returns.reduce((acc, ret) => acc + ret, 0) / returns.length;
  const meanBenchmark = benchmarkReturns.reduce((acc, ret) => acc + ret, 0) / benchmarkReturns.length;
  
  let covariance = 0;
  let benchmarkVariance = 0;
  
  for (let i = 0; i < returns.length; i++) {
    const returnDiff = returns[i] - meanReturn;
    const benchmarkDiff = benchmarkReturns[i] - meanBenchmark;
    covariance += returnDiff * benchmarkDiff;
    benchmarkVariance += benchmarkDiff * benchmarkDiff;
  }
  
  covariance /= returns.length;
  benchmarkVariance /= returns.length;
  
  return benchmarkVariance > 0 ? covariance / benchmarkVariance : 0;
}

/**
 * 计算Alpha系数
 */
function calculateAlpha(
  portfolioReturn: number,
  beta: number,
  benchmarkReturns: number[],
  riskFreeRate: number
): number {
  const benchmarkReturn = benchmarkReturns.reduce((acc, ret) => acc + ret, 0) / benchmarkReturns.length * 252;
  return portfolioReturn - (riskFreeRate + beta * (benchmarkReturn - riskFreeRate));
}

/**
 * 计算跟踪误差
 */
function calculateTrackingError(returns: number[], benchmarkReturns: number[]): number {
  if (returns.length !== benchmarkReturns.length || returns.length === 0) return 0;
  
  const activeReturns = returns.map((ret, i) => ret - benchmarkReturns[i]);
  const meanActiveReturn = activeReturns.reduce((acc, ret) => acc + ret, 0) / activeReturns.length;
  const variance = activeReturns.reduce((acc, ret) => acc + Math.pow(ret - meanActiveReturn, 2), 0) / activeReturns.length;
  
  return Math.sqrt(variance * 252); // 年化跟踪误差
}