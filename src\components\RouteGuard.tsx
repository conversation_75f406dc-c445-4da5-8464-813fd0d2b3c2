import { createEffect, JSX } from solid-js';
import { useNavigate, useLocation } from '@solidjs/router';
import { userStore } from '../stores';

interface RouteGuardProps {
  children: JSX.Element;
  requireAuth?: boolean;
  requireRoles?: string[];
  redirectTo?: string;
}

export default function RouteGuard(props: RouteGuardProps) {
  const navigate = useNavigate();
  const location = useLocation();
  ''
  const {''
    requireAuth = false,
    requireRoles = [], '''
    redirectTo ='/login''
  } = props;

  createEffect(() => {
    const isAuthenticated = userStore.state.isAuthenticated;
    const user = userStore.state.user;
    
    // 如果需要认证但用户未登录''
    if (requireAuth && !isAuthenticated) {'''
      console.log(Route guard: User not authenticated, redirecting to login);
      navigate(redirectTo + `?redirect=${encodeURIComponent(location.pathname)}`);
      return;
    }

    // 如果需要特定角色但用户角色不匹配
    if (requireRoles.length > 0 && user) {
      const hasRequiredRole = requireRoles.includes(user.role);
      if (!hasRequiredRole) {`
        console.log(Route guard: User does not have required role, redirecting);
        navigate('/unauthorized);
        return;
      }
    }
  });

  return <>{props.children}</>;
}

// 便捷的高阶组件
export function withAuth<T extends Record<string, any>>(
  Component: (props: T) => JSX.Element,
  options: { requireRoles?: string[] } = {}
) {
  return (props: T) => (
    <RouteGuard requireAuth={true} requireRoles={options.requireRoles}>
      <Component {...props} />
    </RouteGuard>
  );
}

// 预定义的守卫组件
export const AdminGuard = (props: { children: JSX.Element }) => (
  <RouteGuard requireAuth={true} requireRoles={[admin]}>
    {props.children}
  </RouteGuard>);
'`
export const TraderGuard = (props: { children: JSX.Element }) => (``
  <RouteGuard requireAuth={true} requireRoles={[admin,trader']}>'
    {props.children}
  </RouteGuard>
);

export const AuthGuard = (props: { children: JSX.Element }) => (
  <RouteGuard requireAuth={true}>
    {props.children}
  </RouteGuard>'`
);