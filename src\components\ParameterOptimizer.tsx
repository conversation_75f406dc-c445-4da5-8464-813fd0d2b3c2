import { createSignal, createMemo, For, Show } from 'solid-js';
import { css } from '../../styled-system/css';
import { 
  GridSearchOptimizer, 
  GeneticAlgorithmOptimizer, 
  BayesianOptimizer,
  ParameterRange,
  OptimizationConfig,
  OptimizationResult'
} from '../utils/parameter-optimization';

interface ParameterOptimizerProps {
  onOptimizationComplete?: (results: OptimizationResult[]) => void;
}

export default function ParameterOptimizer(props: ParameterOptimizerProps) {'
  const [optimizationMethod, setOptimizationMethod] = createSignal<'grid' |'genetic' |'bayesian'>('grid');
  const [targetMetric, setTargetMetric] = createSignal('sharpeRatio');
  const [maxIterations, setMaxIterations] = createSignal(100);
  const [isOptimizing, setIsOptimizing] = createSignal(false);
  const [optimizationProgress, setOptimizationProgress] = createSignal(0);
  const [results, setResults] = createSignal<OptimizationResult[]>([]);
  '
 ''// 参数范围配置
  const [parameterRanges, setParameterRanges] = createSignal<ParameterRange[]>([
    { name: shortPeriod, min: 5, max: 20, step: 1, type: 'int' },'
    { name: 'longPeriod', min: 20, max: 60, step: 1, type: 'int' },'
    { name: 'stopLoss', min: 0.02, max: 0.1, step: 0.01, type: 'float' },'
    { name: 'takeProfit', min: 0.05, max: 0.2, step: 0.01, type: 'float' }
  ]);

  // 添加新参数范围
  const addParameterRange = () => {
    setParameterRanges(prev => [...prev, {
      name: `param${prev.length + 1}`,
      min: 0,
      max: 100,
      step: 1,
      type: int
    }]);
  };

  // 删除参数范围
  const removeParameterRange = (index: number) => {
    setParameterRanges(prev => prev.filter((_, i) => i !== index));
  };

  // 更新参数范围
  const updateParameterRange = (index: number, field: keyof ParameterRange, value: any) => {
    setParameterRanges(prev => prev.map((range, i) => 
      i === index ? { ...range, [field]: value } : range
    ));
  };

  // 模拟策略评估函数
  const evaluateStrategy = async (params: Record<string, number>) => {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 模拟回测结果
    const randomFactor = Math.random();
    const totalReturn = (randomFactor - 0.3) * 0.5; // -15% to +20%
    const volatility = 0.1 + randomFactor * 0.2; // 10% to 30%
    const sharpeRatio = totalReturn / volatility;
    const maxDrawdown = -randomFactor * 0.15; // 0% to -15%
    const calmarRatio = totalReturn / Math.abs(maxDrawdown);
    
    return {
      totalReturn,
      sharpeRatio,
      calmarRatio,
      volatility,
      maxDrawdown,
      winRate: 0.4 + randomFactor * 0.4 // 40% to 80%
    };
  };

  // 开始优化
  const startOptimization = async () => {
    setIsOptimizing(true);
    setOptimizationProgress(0);
    setResults([]);

    const config: OptimizationConfig = {
      method: optimizationMethod(),
      maxIterations: maxIterations(),
      targetMetric: targetMetric(),
      maximize : [sharpeRatio,calmarRatio,totalReturn,winRate].includes(targetMetric())
    };

    try {
      let optimizer;
      
      switch (optimizationMethod()) {'
        case'grid: optimizer = new GridSearchOptimizer(parameterRanges(), evaluateStrategy);
          break;
        case genetic: optimizer = new GeneticAlgorithmOptimizer(parameterRanges(), evaluateStrategy);
          break;
        case'bayesian: optimizer = new BayesianOptimizer(parameterRanges(), evaluateStrategy);
          break;
      }

      const optimizationResults = await optimizer.optimize(config);
      setResults(optimizationResults);
      props.onOptimizationComplete?.(optimizationResults);
    } catch (error) { 
      console.error(优化过程出错: ', error);
    } finally {
      setIsOptimizing(false);
      setOptimizationProgress(100);
    }
  };
'
 ''// 停止优化
  const stopOptimization = () => {
    setIsOptimizing(false);
  };

  const bestResult = createMemo(() => results()[0]);

  return (
    <div class={css({ 
      display: flex,
     flexDirection: column',
      gap: '24px'
    })}>
      {/* 优化配置 */}
      <div class={css({ 
       bg: 'white',
       borderRadius: '12px',
       p: '24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)
      })}>
        <h3 class={css({ 
         fontSize', 18px: '$4',
         fontWeight: '600',
         color: '#262626',
          margin: 0,'
          mb: '20px'
        })}>
          参数优化配置
        </h3>

        <div class={css({ 
         display: 'grid',
         'gridTemplateColumns: repeat(auto-fit, minmax(200px, 1fr)),
         gap: '16px',
          mb: '24px'
        })}>
          {/* 优化方法 */}
          <div>
            <label class={css({ 
             display: 'block',
             fontSize: '14px',
             fontWeight: '500',
             color: '#262626',
              mb: '8px'
            })}>
              优化方法
            </label>
            <select
              value={optimizationMethod()}
              onChange={(e) => setOptimizationMethod(e.target.value as any)}
              class={css({'
               width: ''100%,
               p: '8px 12px',
               border: '1px solid #d9d9d9',
               borderRadius: '6px',
                fontSize: '14px'
              })}
            >
              <option value="grid">网格搜索</option>
              <option value="genetic">遗传算法</option>
              <option value="bayesian">贝叶斯优化</option>
            </select>
          </div>

          {/* 目标指标 */}
          <div>
            <label class={css({ 
             display: 'block',
             fontSize: '14px',
             fontWeight: '500',
             color: '#262626',
              mb: '8px'
            })}>
              目标指标
            </label>
            <select
              value={targetMetric()}
              onChange={(e) => setTargetMetric(e.target.value)}
              class={css({'
               width: ''100%,
               p: '8px 12px',
               border: '1px solid #d9d9d9',
               borderRadius: '6px',
                fontSize: '14px'
              })}
            >
              <option value="sharpeRatio">夏普比率</option>
              <option value="calmarRatio">卡尔马比率</option>
              <option value="totalReturn">总收益率</option>
              <option value="winRate">胜率</option>
            </select>
          </div>

          {/* 最大迭代次数 */}
          <div>
            <label class={css({ 
             display: 'block',
             fontSize: '14px',
             fontWeight: '500',
             color: '#262626',
              mb: '8px'
            })}>
              最大迭代次数
            </label>
            <input
              type='number'
              value={maxIterations()}
              onChange={(e) => setMaxIterations(parseInt(e.target.value))}
              min='10'
              max='1000'
              class={css({'
               width: ''100%,
               p: '8px 12px',
               border: '1px solid #d9d9d9',
               borderRadius: '6px',
                fontSize: '14px'
              })}
            />
          </div>
        </div>

        {/* 参数范围配置 */}
        <div class={css({ mb: '24px' })}>
          <div class={css({ 
           display: 'flex',
           alignItems: 'center',
           justifyContent: 'space-between',
            mb: '16px'
          })}>
            <h4 class={css({ 
             fontSize: '16px',
             fontWeight: '600',
             color: '#262626',
              margin: 0
            })}>
              参数范围
            </h4>
            <button
              onClick={addParameterRange}
              class={css({ 
               px: '12px',
               py: '6px',
               bg: '#1890ff',
               color: 'white',
               border: 'none',
               borderRadius: '6px',
               fontSize: '12px',
               cursor: 'pointer',
                _hover: { bg: '#40a9ff' }
              })}
            >
              添加参数
            </button>
          </div>

          <div class={css({ 
           display: 'flex',
           flexDirection: 'column',
            gap: '12px'
          })}>
            <For each={parameterRanges()}>
              {(range, index) => (
                <div class={css({ 
                 display: 'grid',
                 gridTemplateColumns: '1fr 1fr 1fr 1fr 80px 40px',
                 gap: '8px',
                 alignItems: 'center',
                 p: '12px',
                 bg: '#fafafa',
                  borderRadius: '8px'
                })}>
                  <input
                    type='text'
                    value={range.name}
                    onChange={(e) => updateParameterRange(index(),'name', e.target.value)}
                    placeholder='参数名'
                    class={css({ 
                     p: '6px 8px',
                     border: '1px solid #d9d9d9',
                     borderRadius: '4px',
                      fontSize: '12px'
                    })}
                  />
                  <input
                    type='number'
                    value={range.min}
                    onChange={(e) => updateParameterRange(index(),'min', parseFloat(e.target.value))}
                    placeholder='最小值'
                    step='0.01'
                    class={css({ 
                     p: '6px 8px',
                     border: '1px solid #d9d9d9',
                     borderRadius: '4px',
                      fontSize: '12px'
                    })}
                  />
                  <input
                    type='number'
                    value={range.max}
                    onChange={(e) => updateParameterRange(index(),'max', parseFloat(e.target.value))}
                    placeholder='最大值'
                    step='0.01'
                    class={css({ 
                     p: '6px 8px',
                     border: '1px solid #d9d9d9',
                     borderRadius: '4px',
                      fontSize: '12px'
                    })}
                  />
                  <input
                    type= 'number'
                    value={range.step ||`}
                    onChange={(e) => updateParameterRange(index(),'step', parseFloat(e.target.value) || undefined)}
                    placeholder='步长'
                    step='0.01'
                    class={css({ 
                     p: '6px 8px',
                     border: '1px solid #d9d9d9',
                     borderRadius: '4px',
                      fontSize: '12px'
                    })}
                  />
                  <select
                    value={range.type}
                    onChange={(e) => updateParameterRange(index(),'type', e.target.value as'int' |'float')}
                    class={css({ 
                     p: '6px 8px',
                     border: '1px solid #d9d9d9',
                     borderRadius: '4px',
                      fontSize: '12px'
                    })}
                  >
                    <option value="int">整数</option>
                    <option value="float">小数</option>
                  </select>
                  <button
                    onClick={() => removeParameterRange(index())}
                    class={css({ 
                     p: '6px',
                     bg: '#ff4d4f',
                     color: 'white',
                     border: 'none',
                     borderRadius: '4px',
                     fontSize: '12px',
                     cursor: 'pointer',
                      _hover: { bg: '#ff7875' }
                    })}
                  >
                    ×
                  </button>
                </div>
              )}
            </For>
          </div>
        </div>

        {/* 控制按钮 */}
        <div class={css({ 
         display: 'flex',
          gap: '12px'
        })}>
          <Show
            when={!isOptimizing()}
            fallback={
              <button
                onClick={stopOptimization}
                class={css({ 
                 px: '24px',
                 py: '12px',
                 bg: '#ff4d4f',
                 color: 'white',
                 border: 'none',
                 borderRadius: '8px',
                 fontSize: '14px',
                 fontWeight: '500',
                 cursor: 'pointer',
                  _hover: { bg: '#ff7875' }
                })}
              >
                停止优化
              </button>
            }
          >
            <button
              onClick={startOptimization}
              disabled={parameterRanges().length === 0}
              class={css({ 
               px: '24px',
               py: '12px',
               bg: '#52c41a',
               color: 'white',
               border: 'none',
               borderRadius: '8px',
               fontSize: '14px',
               fontWeight: '500',
               cursor: 'pointer',
                _hover: { bg: '#73d13d' },
                _disabled: { 
                 bg: '#d9d9d9',
                  cursor: 'not-allowed'
                }
              })}
            >
              开始优化
            </button>
          </Show>
        </div>

        {/* 进度条 */}
        <Show when={isOptimizing()}>
          <div class={css({ 
           mt: '16px',
           p: '16px',
           bg: '#f6ffed',
           borderRadius: '8px',
            border: '1px solid #b7eb8f'
          })}>
            <div class={css({ 
             fontSize: '14px',
             color: '#52c41a',
              mb: '8px'
            })}>
              优化进行中... {optimizationProgress()}%
            </div>
            <div class={css({'
             width: `100%,
             height: '8px',
             bg: '#f0f0f0',
             borderRadius: '4px',
              overflow: 'hidden'
            })}>
              <div
                class={css({'
                 height: `100%,
                 bg: '#52c41a',
                  transition: 'width 0.3s ease'
                })}
                style={{ width: `${optimizationProgress()}%` }}
             `/>
            </div>
          </div>
        </Show>
      </div>

      {/* 优化结果 */}
      <Show when={results().length > 0}>
        <div class={css({ 
          bg: 'white',
         borderRadius: '12px',
         p: '24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)
        })}>
          <h3 class={css({ 
           fontSize', 18px: '$4',
           fontWeight: '600',
           color: '#262626',
            margin: 0,'
            mb: '20px'
          })}>
            优化结果
          </h3>

          {/* 最佳结果 */}
          <Show when={bestResult()}>
            <div class={css({ 
             p: '16px',
             bg: '#f6ffed',
             borderRadius: '8px',
             border: '1px solid #b7eb8f',
              mb: '20px'
            })}>
              <h4 class={css({ 
               fontSize: '16px',
               fontWeight: '600',
               color: '#52c41a',
                margin: 0,'
                mb: '12px'
              })}>
                最佳参数组合
              </h4>
              <div class={css({ 
               display: 'grid',
               'gridTemplateColumns: repeat(auto-fit, minmax(150px, 1fr)),
                gap: '12px'
              })}>
                <For each={Object.entries(bestResult()!.parameters)}>
                  {([key, value]) => (
                    <div class={css({ 
                      textAlign: 'center'
                    })}>
                      <div class={css({ 
                       fontSize: '12px',
                       color: '#8c8c8c',
                        mb: '4px'
                      })}>
                        {key}
                      </div>
                      <div class={css({ 
                       fontSize: '16px',
                       fontWeight: '600',
                        color: '#262626'
                      })}>
                        {typeof value ==='number' ? value.toFixed(3) : value}
                      </div>
                    </div>
                  )}
                </For>
              </div>
              <div class={css({ 
               mt: '12px',
                textAlign: 'center'
              })}>
                <span class={css({ 
                 fontSize: '14px',
                  color: '#8c8c8c'
                })}>
                  {targetMetric()}: 
                </span>
                <span class={css({ 
                 fontSize: '18px',
                 fontWeight: '700',
                 color: '#52c41a',
                  ml: '8px'
                })}>
                  {bestResult()!.score.toFixed(4)}
                </span>
              </div>
            </div>
          </Show>

          {/* 结果列表 */}
          <div class={css({ 
           maxHeight: '400px',
            overflowY: 'auto'
          })}>
            <For each={results().slice(0, 10)}>
              {(result, index) => (
                <div class={css({ 
                 display: 'flex',
                 alignItems: 'center',
                 justifyContent: 'space-between',
                 p: '12px',
                 borderBottom: '1px solid #f0f0f0',
                  _hover: { bg: '#fafafa' }
                })}>
                  <div class={css({ 
                   display: 'flex',
                   alignItems: 'center',
                    gap: '12px'
                  })}>
                    <div class={css({ 
                     width: '24px',
                     height: '24px',
                      bg : index() === 0 ? '#52c41a: '#f0f0f0',
                      color : index() === 0 ?white: '#8c8c8c',
                     borderRadius: `50%,
                     display: 'flex',
                     alignItems: 'center',
                     justifyContent: 'center',
                     fontSize: '12px',
                      fontWeight: '600'
                    })}>
                      {index() + 1}
                    </div>
                    <div class={css({ 
                     fontSize: '14px',
                      color: '#262626'
                    })}>
                      {Object.entries(result.parameters)
                        .map(([key, value]) => `${key}=${typeof value ==='number' ? value.toFixed(2) : value}`)
                        .join(,)}
                    </div>
                  </div>
                  <div class={css({ 
                    fontSize: '16px',
                   fontWeight: '600',
                    color : index() === 0 ? '#52c41a: '#262626'
                  })}>
                    {result.score.toFixed(4)}
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </Show>
    </div>
  );
}
