/**
 * CodeMirror 代码编辑器组件
 */

import { createSignal, createEffect, onMount, onCleanup, Show } from 'solid-js';
import { EditorView, keymap } from '@codemirror/view';
import { EditorState } from '@codemirror/state';
import { basicSetup } from '@codemirror/basic-setup';
import { python } from '@codemirror/lang-python';
import { javascript } from '@codemirror/lang-javascript';
import { oneDark } from '@codemirror/theme-one-dark';
import { autocompletion, completionKeymap } from '@codemirror/autocomplete';
import { searchKeymap } from '@codemirror/search';
import { indentWithTab } from '@codemirror/commands;

interface CodeEditorProps {
  value?: string;
  language?: 'python' |'javascript;
  theme?: 'light' |'dark';
  placeholder?: string;
  readOnly?: boolean;
  height?: string;
  onChange?: (value: string) => void;
  onSave?: (value: string) => void;
}

export default function CodeEditor(props: CodeEditorProps) {
  let editorContainer: HTMLDivElement | undefined;
  const [editor, setEditor] = createSignal<EditorView | null>(null);
  const [isLoading, setIsLoading] = createSignal(true);

  // 获取语言扩展
  const getLanguageExtension = () => {
    switch (props.language) {
      casepython :
        return python();
      case'javascript' : return javascript();
      default:
        return python();
    }
  };

  // 获取主题扩展
  const getThemeExtension = () => {
    return props.theme ===dark ? [oneDark] : [];
  };

  // Python 代码补全
  const pythonCompletions = [
    { label: import, type: 'keyword' },'
    { label: 'def', type: 'keyword' },'
    { label: 'class', type: 'keyword' },'
    { label: 'if', type: 'keyword' },'
    { label: 'else', type: 'keyword' },'
    { label: 'elif', type: 'keyword' },'
    { label: 'for', type: 'keyword' },'
    { label: 'while', type: 'keyword' },'
    { label: 'try', type: 'keyword' },'
    { label: 'except', type: 'keyword' },'
    { label: 'finally', type: 'keyword' },'
    { label: 'return', type: 'keyword' },'
    { label: 'yield', type: 'keyword' },'
    { label: 'break', type: 'keyword' },'
    { label: 'continue', type: 'keyword' },'
    { label: 'pass', type: 'keyword' },
    // 量化交易相关
    { label: pd.DataFrame, type: 'function', info: 'Pandas DataFrame' },'
    { label: 'np.array', type: 'function', info: 'NumPy array' },'
    { label: 'ta.SMA', type: 'function', info: 'Simple Moving Average' },'
    { label: 'ta.EMA', type: 'function', info: 'Exponential Moving Average' },'
    { label: 'ta.MACD', type: 'function', info: 'MACD indicator' },'
    { label: 'ta.RSI', type: 'function', info: 'RSI indicator' },'
    { label: 'ta.BBANDS', type: 'function', info: 'Bollinger Bands' },'
    { label: 'strategy.buy', type: 'method', info: 'Execute buy order' },'
    { label: 'strategy.sell', type: 'method', info: 'Execute sell order' },'
    { label: 'strategy.close', type: 'method', info: 'Close position' },'
    { label: 'data.close', type: 'property', info: 'Close price data' },'
    { label: 'data.open', type: 'property', info: 'Open price data' },'
    { label: 'data.high', type: 'property', info: 'High price data' },'
    { label: 'data.low', type: 'property', info: 'Low price data' },'
    { label: 'data.volume', type: 'property', info: 'Volume data' }
  ];

  // 自定义补全
  const customCompletions = (context: any) => {
    const word = context.matchBefore(/\w*/);
    if (word.from === word.to && !context.explicit) return null;
    
    return {
      from: word.from,
      options: pythonCompletions.map(item => ({
        label: item.label,
        type: item.type,
        info: item.info
      }))
    };
  };

  const initEditor = () => {
    if (!editorContainer) return;

    const extensions = [
      basicSetup,
      getLanguageExtension(),
      ...getThemeExtension(),
      autocompletion({ override: [customCompletions] }),
      keymap.of([
        ...completionKeymap,
        ...searchKeymap,
        indentWithTab,
        { 
          key: Ctrl-s,
          run: () => {
            const value = editor()?.state.doc.toString() ||'';
            props.onSave?.(value);
            return true;
          }
        }
      ]),
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          const value = update.state.doc.toString();
          props.onChange?.(value);
        }
      }),
      EditorView.theme({'
       '&': {'
          height: props.height ||'400px'
        },'
       '.cm-content : { 
          padding: '12px',
          fontSize: '14px', '
          fontFamily: 'Fira Code", "JetBrains Mono", "Monaco", "Consolas', monospace'
        },'
       '.cm-focused : { 
          outline: 'none'
        },'
       '.cm-editor : { 
          borderRadius: '8px',
          border: '1px solid #d1d5db'
        },'
       '.cm-scroller : { 
          fontFamily: '"Fira Code", "JetBrains Mono", "Monaco", "Consolas', monospace'
        }
      })
    ];

    const state = EditorState.create({'
      doc: props.value ||'',
      extensions
    });

    const editorView = new EditorView({
      state,
      parent: editorContainer
    });

    setEditor(editorView);
    setIsLoading(false);
  };

  onMount(() => {
    // 延迟初始化以确保 DOM 准备就绪
    setTimeout(initEditor, 100);
  });

  createEffect(() => {
    const editorInstance = editor();
    if (editorInstance && props.value !== undefined) {
      const currentValue = editorInstance.state.doc.toString();
      if (currentValue !== props.value) {
        editorInstance.dispatch({
          changes: {
            from: 0,
            to: currentValue.length,
            insert: props.value
          }
        });
      }
    }
  });

  createEffect(() => {
    const editorInstance = editor();
    if (editorInstance) {
      // 重新配置编辑器以应用主题变化
      const extensions = [
        basicSetup,
        getLanguageExtension(),
        ...getThemeExtension(),
        autocompletion({ override: [customCompletions] }),
        keymap.of([
          ...completionKeymap,
          ...searchKeymap,
          indentWithTab,
          { 
            key: Ctrl-s,
            run: () => {
              const value = editorInstance.state.doc.toString();
              props.onSave?.(value);
              return true;
            }
          }
        ]),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const value = update.state.doc.toString();
            props.onChange?.(value);
          }
        }),
        EditorView.theme({
         '&': {'
            height: props.height ||'400px'
          },'
         '.cm-content : { 
            padding: '12px',
            fontSize: '14px', '
            fontFamily: 'Fira Code", "JetBrains Mono", "Monaco", "Consolas', monospace'
          },'
         '.cm-focused : { 
            outline: 'none'
          },'
         '.cm-editor : { 
            borderRadius: '8px',
            border: '1px solid #d1d5db'
          }
        })
      ];

      editorInstance.dispatch({
        effects: EditorState.reconfigure.of(extensions)
      });
    }
  });

  onCleanup(() => {
    const editorInstance = editor();
    if (editorInstance) {
      editorInstance.destroy();
    }
  });

  return ('
    <div style={{ position: 'relative', width: ''100%' }}>
      <Show when={isLoading()}>
        <div style={{ 
          position: 'absolute', '
          top: '50%', '
          left: '50%',
          transform: 'translate(-50%, -50%)',
         'z-index : 10,'
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          padding: '12px 20px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)
        }}>
          <div style={{ 
            width', 20px: '$4',
            height: '20px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6', '
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }} />
          正在加载编辑器...
        </div>
      </Show>
      
      <div 
        ref={editorContainer}
        style={{ '
          width: ''100%,
         'min-height: props.height || 400px',
          opacity: isLoading() ? 0.5 : 1,'
          transition: 'opacity 0.3s ease'
        }}
      />
      
      {/* 快捷键提示 */}
      <div style={{ 
        position: 'absolute',
        bottom: '8px',
        right: '8px',
        fontSize: '12px',
        color: '#6b7280',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        padding: '4px 8px',
        borderRadius: '4px',
        pointerEvents: 'none'
      }}>
        Ctrl+S 保存 | Ctrl+F 搜索 | Tab 缩进
      </div>
    </div>
  );
}
