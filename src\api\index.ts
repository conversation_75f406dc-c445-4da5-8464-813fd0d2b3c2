/**
 * API统一入口
 */

// 导出HTTP客户端
export { httpClient, HttpClient } from ../utils/http
export type { ApiResponse, ListResponse, PaginatedResponse, RequestConfig, ApiError } from ../utils/http'
// 导出常量
export { API_PATHS, WS_PATHS, ENV_CONFIG, MARKET_CONFIG, TRADING_CONFIG, STRATEGY_CONFIG, BACKTEST_CONFIG } from ../utils/constants
// 导入和导出市场数据API
import { marketApi, MarketAPI } from ./market
export { marketApi, MarketAPI }
export type {
  QuoteData,
  KLineData,
  OrderBookData,
  OrderBookItem,
  StockInfo,
  MarketOverview,
  SectorData,
  NewsItem,
  QuoteParams,
  KLineParams,
  SearchParams'
} from './market'
// 导入和导出策略管理API
import { strategyApi, StrategyAPI } from ./strategy
export { strategyApi, StrategyAPI }
export type {
  Strategy,
  StrategyType,
  StrategyStatus,
  RiskLevel,
  StrategyFrequency,
  StrategyParameter,
  StrategyConfig,
  StrategyPerformance,
  StrategySignal,
  StrategyTemplate,
  StrategyListParams,
  CreateStrategyRequest,
  UpdateStrategyRequest'
} from './strategy'
// 导入和导出回测分析API
import { backtestApi, BacktestAPI } from ./backtest
export { backtestApi, BacktestAPI }
export type {
  BacktestResult,
  BacktestStatus,
  BacktestConfig,
  BacktestTrade,
  BacktestTask,
  BacktestListParams,
  CreateBacktestRequest'
} from './backtest'
// 导入和导出用户管理API
import { userApi, UserAPI } from ./user
export { userApi, UserAPI }
export type {
  UserData,
  UserPreferences,
  UserProfile,
  LoginRequest,
  RegisterRequest,
  PasswordResetRequest,
  PasswordChangeRequest,
  ProfileUpdateRequest,
  PreferencesUpdateRequest,
  LoginResponse,
  TokenRefreshResponse'
} from './user'
// 导入和导出WebSocket管理
import {
  WebSocketManager,
  MarketWebSocket,
  TradingWebSocket,
  StrategyWebSocket,
  marketWS,
  tradingWS,
  strategyWS
} from ../utils/websocket
export {
  WebSocketManager,
  MarketWebSocket,
  TradingWebSocket,
  StrategyWebSocket,
  marketWS,
  tradingWS,
  strategyWS
}
export type {
  WSMessage,
  WSEventType,
  WSEventListener,
  WSConfig,
  WSConnectionState'
} from '../utils/websocket'
/**
 * API管理器类
 * 提供统一的API访问接口
 */
export class APIManager {
  // API实例
  public market = marketApi
  public strategy = strategyApi
  public backtest = backtestApi
  public user = userApi

  // WebSocket实例
  public marketWS = marketWS
  public tradingWS = tradingWS
  public strategyWS = strategyWS

  /**
   * 初始化API管理器
   */
  async initialize(): Promise<void> {
    try {
      // 检查用户登录状态
      const token = typeof window !== undefined ? localStorage.getItem('access_token') : null
      if (token) {
        // 验证token有效性
        try {
          await this.user.getUserInfo()
        } catch (error) {
          // token无效，清除本地存储
          if (typeof window !== undefined) {
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            localStorage.removeItem('user_info')
          }
        }
      }

      // 连接WebSocket（如果用户已登录）
      if (token) {
        this.connectWebSockets()
      }

      console.log(API管理器初始化完成)
    } catch (error) {'
      console.error('API管理器初始化失败:', error)
    }
  }

  /**
   * 连接WebSocket
   */
  connectWebSockets(): void {
    try {
      // 连接市场数据WebSocket
      this.marketWS.connect()
      
      // 设置错误处理
      this.marketWS.on(error, (error) => {'
        console.error('市场数据WebSocket连接错误:', error)
      })
'
      this.marketWS.on('reconnect', (data) => {
        console.log(`市场数据WebSocket重连中... (${data.attempt}/${data.maxAttempts})`)
      })
'
      console.log('WebSocket连接已建立')
    } catch (error) {'
      console.error('WebSocket连接失败:', error)
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnectWebSockets(): void {
    try {
      this.marketWS.disconnect()
      this.tradingWS.disconnect()
      this.strategyWS.disconnect()
      console.log('WebSocket连接已断开')
    } catch (error) {'
      console.error('断开WebSocket连接失败:', error)
    }
  }

  /**
   * 用户登录后的初始化
   */
  async onUserLogin(): Promise<void> {
    try {
      // 连接WebSocket
      this.connectWebSockets()
      
      // 可以在这里添加其他登录后的初始化逻辑
      console.log(用户登录后初始化完成)
    } catch (error) {'
      console.error('用户登录后初始化失败:', error)
    }
  }

  /**
   * 用户登出后的清理
   */
  async onUserLogout(): Promise<void> {
    try {
      // 断开WebSocket连接
      this.disconnectWebSockets()
      
      // 清除本地缓存
      if (typeof window !== undefined) {
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_info')
      }
      '
      console.log('用户登出后清理完成')
    } catch (error) {'
      console.error('用户登出后清理失败:', error)
    }
  }

  /**
   * 获取API健康状态
   */
  async getHealthStatus(): Promise<{
    api: boolean
    websocket: boolean
    services: {
      market: boolean
      strategy: boolean
      backtest: boolean
      user: boolean
    }
  }> {
    const status = {
      api: false,
      websocket: false,
      services: {
        market: false,
        strategy: false,
        backtest: false,
        user: false
      }
    }

    try {
      // 检查API服务状态
      const promises = [
        this.market.getOverview().then(() => { status.services.market = true }).catch(() => {}),
        this.strategy.getTemplates().then(() => { status.services.strategy = true }).catch(() => {}),
        this.backtest.getHealth().then(() => { status.services.backtest = true }).catch(() => {}),
        this.user.getUserInfo().then(() => { status.services.user = true }).catch(() => {})
      ]

      await Promise.allSettled(promises)

      // 检查整体API状态
      status.api = Object.values(status.services).some(s => s)

      // 检查WebSocket状态
      status.websocket = this.marketWS.getState() === connected
      return status
    } catch (error) {'
      console.error('获取API健康状态失败:', error)
      return status
    }
  }
}

// 创建全局API管理器实例
export const apiManager = new APIManager()

// 导出默认实例
export default apiManager

/**
 * 便捷的API访问函数
 */

// 市场数据
export const getQuote = (symbols: string | string[]) => apiManager.market.getQuote(symbols)
export const getKLineData = (params: any) => apiManager.market.getKLineData(params)
export const searchStocks = (params: any) => apiManager.market.search(params)
export const getMarketOverview = () => apiManager.market.getOverview()

// 策略管理
export const getStrategies = (params?: any) => apiManager.strategy.getStrategies(params)
export const getStrategy = (id: string) => apiManager.strategy.getStrategy(id)
export const createStrategy = (data: any) => apiManager.strategy.createStrategy(data)
export const updateStrategy = (data: any) => apiManager.strategy.updateStrategy(data)
export const deleteStrategy = (id: string) => apiManager.strategy.deleteStrategy(id)
export const getStrategyTemplates = () => apiManager.strategy.getTemplates()

// 回测分析
export const getBacktests = (params?: any) => apiManager.backtest.getBacktests(params)
export const getBacktestResult = (id: string) => apiManager.backtest.getBacktestResult(id)
export const createBacktest = (data: any) => apiManager.backtest.createBacktest(data)
export const startBacktest = (id: string) => apiManager.backtest.startBacktest(id)
export const stopBacktest = (id: string) => apiManager.backtest.stopBacktest(id)

// 用户管理
export const login = (data: any) => apiManager.user.login(data)
export const register = (data: any) => apiManager.user.register(data)
export const logout = () => apiManager.user.logout()
export const getUserInfo = () => apiManager.user.getUserInfo()
export const updateProfile = (data: any) => apiManager.user.updateProfile(data)
export const changePassword = (data: any) => apiManager.user.changePassword(data)