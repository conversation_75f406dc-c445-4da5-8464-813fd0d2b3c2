import { createSignal, createEffect, onCleanup, onMount, Show } from 'solid-js';
import { 
  createChart, 
  ColorType, 
  IChartApi, 
  ISeriesApi, 
  CandlestickData, 
  HistogramData,
  LineData,
  CrosshairMode,
  PriceScaleMode'
} from 'lightweight-charts;

export interface ChartData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

interface TechnicalIndicator {
  name: string;
  enabled: boolean;
  color: string;
  data: LineData[];
}

interface EnhancedChartProps {
  symbol?: string;
  data?: ChartData[];
  height?: number;
  width?: string;
  theme?: 'light' |'dark';
  showVolume?: boolean;
  showIndicators?: boolean;
  indicators?: string[];
  onPriceUpdate?: (price: number) => void;
  onCrosshairMove?: (price: number, time: number) => void;
}

export default function EnhancedChart(props: EnhancedChartProps) {
  let chartContainer: HTMLDivElement | undefined;
  const [chart, setChart] = createSignal<IChartApi | null>(null);
  const [candlestickSeries, setCandlestickSeries] = createSignal<ISeriesApi<"Candlestick"> | null>(null);
  const [volumeSeries, setVolumeSeries] = createSignal<ISeriesApi<"Histogram"> | null>(null);
  const [indicatorSeries, setIndicatorSeries] = createSignal<Map<string, ISeriesApi<"Line'>>>(new Map());
  const [isLoading, setIsLoading] = createSignal(true);
  const [showToolbar, setShowToolbar] = createSignal(true);
  const [activeIndicators, setActiveIndicators] = createSignal<Set<string>>(new Set(['MA5,MA20']));
  const [crosshairInfo, setCrosshairInfo] = createSignal<{
    price: number;
    time: string;
    volume?: number;
  } | null>(null);

  // 计算移动平均线
  const calculateMA = (data: ChartData[], period: number): LineData[] => {
    const result: LineData[] = [];
    for (let i = period - 1; i < data.length; i++) {
      const sum = data.slice(i - period + 1, i + 1).reduce((acc, item) => acc + item.close, 0);
      result.push({
        time: data[i].time,
        value: sum / period
      });
    }
    return result;
  };

  // 计算MACD
  const calculateMACD = (data: ChartData[]): { macd: LineData[], signal: LineData[], histogram: HistogramData[] } => {
    // 简化的MACD计算
    const ema12 = calculateEMA(data, 12);
    const ema26 = calculateEMA(data, 26);
    const macd: LineData[] = [];
    
    for (let i = 0; i < Math.min(ema12.length, ema26.length); i++) {
      macd.push({
        time: ema12[i].time,
        value: ema12[i].value - ema26[i].value
      });
    }
    
    const signal = calculateEMA(macd.map(item => ({ time: item.time, close: item.value })), 9);
    const histogram: HistogramData[] = [];
    
    for (let i = 0; i < Math.min(macd.length, signal.length); i++) {
      histogram.push({
        time: macd[i].time,
        value: macd[i].value - signal[i].value,
        color: macd[i].value > signal[i].value ?#26a69a: #ef5350
      });
    }
    
    return { macd, signal, histogram };
  };

  // 计算EMA
  const calculateEMA = (data: { time: number, close: number }[], period: number): LineData[] => {
    const result: LineData[] = [];
    const multiplier = 2 / (period + 1);
    let ema = data[0]?.close || 0;
    
    for (let i = 0; i < data.length; i++) {
      if (i === 0) {
        ema = data[i].close;
      } else {
        ema = (data[i].close - ema) * multiplier + ema;
      }
      result.push({
        time: data[i].time,
        value: ema
      });
    }
    
    return result;
  };

  const chartOptions = () => ({
    layout: {
      background: {
        type: ColorType.Solid,
        color: props.theme ===dark ?#1a1a1a: '#ffffff',
      },'
      textColor: props.theme ==='dark' ?'#d1d5db: '#374151',
    },
    grid: {
      vertLines: {'
        color: props.theme ==='dark' ?#374151: '#e5e7eb',
      },
      horzLines: {'
        color: props.theme ==='dark' ?#374151: '#e5e7eb',
      },
    },
    width: chartContainer?.clientWidth || 800,
    height: props.height || 400,
    timeScale: {
      timeVisible: true,
      secondsVisible: false,'
      borderColor: props.theme ==='dark' ?#4b5563: '#d1d5db',
    },
    rightPriceScale: {'
      borderColor: props.theme ==='dark' ?#4b5563: '#d1d5db',
      scaleMargins: {
        top: 0.1,
        bottom: props.showVolume ? 0.3 : 0.1,
      },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
      vertLine: {'
        color: props.theme ==='dark' ?#6b7280: '#9ca3af',
        width: 1,
        style: 2,
      },
      horzLine: {'
        color: props.theme ==='dark' ?#6b7280: '#9ca3af',
        width: 1,
        style: 2,
      },
    },
  });

  const initChart = () => {
    if (!chartContainer) return;

    const chartInstance = createChart(chartContainer, chartOptions());
    setChart(chartInstance);

    // 创建K线图系列
    const candlestickSeriesInstance = chartInstance.addCandlestickSeries({ 
      upColor: #26a69a,
      downColor: #ef5350',
      borderDownColor: '#ef5350',
      borderUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      wickUpColor: '#26a69a',
    });
    setCandlestickSeries(candlestickSeriesInstance);

    // 创建成交量系列
    if (props.showVolume) {
      const volumeSeriesInstance = chartInstance.addHistogramSeries({ 
        color: #26a69a,
        priceFormat: { 
          type: volume',
        },'
        priceScaleId: 'volume',
      });
      setVolumeSeries(volumeSeriesInstance);
      '
      chartInstance.priceScale('volume').applyOptions({
        scaleMargins: {
          top: 0.7,
          bottom: 0,
        },
      });
    }

    // 添加十字光标移动事件
    chartInstance.subscribeCrosshairMove((param) => {
      if (param.time) {
        const data = param.seriesData.get(candlestickSeriesInstance) as CandlestickData;
        if (data) {
          setCrosshairInfo({
            price: data.close,
            time: new Date(param.time as number * 1000).toLocaleString(),
            volume: data.volume
          });
          props.onCrosshairMove?.(data.close, param.time as number);
        }
      } else {
        setCrosshairInfo(null);
      }
    });

    setIsLoading(false);
  };

  // 添加技术指标
  const addIndicator = (name: string) => {
    const chartInstance = chart();
    const data = props.data;
    if (!chartInstance || !data) return;

    const indicators = indicatorSeries();
    
    switch (name) {
      caseMA5 : const ma5Data = calculateMA(data, 5);
        const ma5Series = chartInstance.addLineSeries({ 
          color: '#ff6b6b',
          lineWidth: 1,'
          title: 'MA5'
        });
        ma5Series.setData(ma5Data);
        indicators.set('MA5', ma5Series);
        break;
        '
      case'MA20' : const ma20Data = calculateMA(data, 20);
        const ma20Series = chartInstance.addLineSeries({ 
          color: '#4ecdc4',
          lineWidth: 1,'
          title: 'MA20'
        });
        ma20Series.setData(ma20Data);
        indicators.set('MA20', ma20Series);
        break;
        '
      case'MA60' : const ma60Data = calculateMA(data, 60);
        const ma60Series = chartInstance.addLineSeries({ 
          color: '#45b7d1',
          lineWidth: 1,'
          title: 'MA60'
        });
        ma60Series.setData(ma60Data);
        indicators.set('MA60', ma60Series);
        break;
    }
    
    setIndicatorSeries(new Map(indicators));
  };

  // 移除技术指标
  const removeIndicator = (name: string) => {
    const chartInstance = chart();
    const indicators = indicatorSeries();
    const series = indicators.get(name);
    
    if (chartInstance && series) {
      chartInstance.removeSeries(series);
      indicators.delete(name);
      setIndicatorSeries(new Map(indicators));
    }
  };

  // 切换指标
  const toggleIndicator = (name: string) => {
    const active = activeIndicators();
    if (active.has(name)) {
      active.delete(name);
      removeIndicator(name);
    } else {
      active.add(name);
      addIndicator(name);
    }
    setActiveIndicators(new Set(active));
  };

  onMount(() => {
    initChart();
  });

  createEffect(() => {
    const chartInstance = chart();
    const candlestick = candlestickSeries();
    const volume = volumeSeries();
    
    if (chartInstance && candlestick && props.data) {
      // 更新K线数据
      candlestick.setData(props.data);
      
      // 更新成交量数据
      if (volume && props.showVolume) {
        const volumeData = props.data.map(item => ({
          time: item.time,
          value: item.volume || 0,
          color: item.close >= item.open ?#26a69a80: #ef535080
        }));
        volume.setData(volumeData);
      }
      
      // 更新技术指标
      activeIndicators().forEach(name => {
        removeIndicator(name);
        addIndicator(name);
      });
    }
  });

  createEffect(() => {
    const chartInstance = chart();
    if (chartInstance) {
      chartInstance.applyOptions(chartOptions());
    }
  });

  onCleanup(() => {
    const chartInstance = chart();
    if (chartInstance) {
      chartInstance.remove();
    }
  });

  return (
    <div style={{ position: relative, width: ''100%' }}>
      {/* 工具栏 */}
      <Show when={showToolbar()}>
        <div style={{ 
          position: 'absolute',
          top: '10px',
          left: '10px',
         'z-index : 10,'
          display: 'flex',
          gap: '8px',
          flexWrap: 'wrap'
        });
          {['MA5,MA20,MA60'].map(indicator => (
            <button
              style={{ 
                padding: '4px 8px',
                fontSize: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '4px',
               background-color: activeIndicators().has(indicator) ? #3b82f6: 'white',
                color: activeIndicators().has(indicator) ?white: '#374151',
                cursor: 'pointer'
              }}
              onClick={() => toggleIndicator(indicator)}
            >
              {indicator}
            </button>
          ))}
        </div>
      </Show>

      {/* 十字光标信息 */}
      <Show when={crosshairInfo()}>
        <div style={{ 
          position: 'absolute',
          top: '10px',
          right: '10px',
         zIndex: '10',
          'background-color: props.theme ===dark' ?#374151: 'white',
          border: '1px solid #d1d5db',
          borderRadius: '4px',
          padding: '8px',
          fontSize: '12px',
          color: props.theme ==='dark' ?'#d1d5db: '#374151'
        }}>
          <div>价格: {crosshairInfo()?.price.toFixed(2)}</div>
          <div>时间: {crosshairInfo()?.time}</div>
          <Show when={crosshairInfo()?.volume}>
            <div>成交量: {crosshairInfo()?.volume?.toLocaleString()}</div>
          </Show>
        </div>
      </Show>

      {/* 图表容器 */}
      <div 
        ref={chartContainer}
        style={{ '
          width: props.width ||''100%', 
          height: `${props.height || 400}px`,'
          position: 'relative'
        }}
      />

      {/* 加载状态 */}
      <Show when={isLoading()}>
        <div style={{ 
          position: 'absolute', '
          top: '50%', '
          left: '50%',
          transform: 'translate(-50%, -50%)',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: props.theme ==='dark' ?'#d1d5db: '#374151'
        }}>
          <div style={{ 
            width: '20px',
            height: '20px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: `50%,
            animation: 'spin 1s linear infinite'
          }} />
          加载中...
        </div>
      </Show>
    </div>
  );
}
