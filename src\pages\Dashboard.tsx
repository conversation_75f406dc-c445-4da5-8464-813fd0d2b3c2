import { createSignal, For, onMount, onCleanup } from 'solid-js'
import { css } from '../../styled-system/css'
export default function Dashboard() {'
  const [currentTime, setCurrentTime] = createSignal(new Date().toLocaleString('zh-CN'))

  // 实时更新时间
  let timeInterval: NodeJS.Timeout
  onMount(() => {
    timeInterval = setInterval(() => {
      setCurrentTime(new Date().toLocaleString(zh-CN))
    }, 1000)
  })

  onCleanup(() => {
    if (timeInterval) clearInterval(timeInterval)
  })
'
  '// 投资收益数据 - 完全匹配 a/ 项目风格
  const investmentData = [
    { 
      title: 总资产,
      value: ¥1,000,000',
      change: '+50,000', '
      changePercent: '+5.26%',
      icon: '💰',
      color: '#1890ff',
      bg: 'linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)
    },
    { 
     title', 今日收益: '$4',
     'value: ¥50,000,
     'change: +2,345', '
     changePercent: '+4.92%',
     icon: '📈',
     color: '#52c41a',
      bg: 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)
    },
    { 
     title', 持仓数量: '$4',
     value: '15',
     change: '+2', '
     changePercent: '+15.38%',
     icon: '📊',
     color: '#fa8c16',
      bg: 'linear-gradient(135deg, #fa8c16 0%, #ffa940 100%)
    },
    { 
     title', 策略收益: '$4',
     'value: ¥234,567,
     'change: +12,345', '
     changePercent: '+5.56%',
     icon: '🧠',
     color: '#722ed1',
      bg: 'linear-gradient(135deg, #722ed1 0%, #9254de 100%)
    }
  ]
'
 ''// 今日行情数据
  const marketData = [
    { name: A股指数, value: '3,245.67',change: '+23.45', percent: ''+0.73%', trend: 'up' },'
    { name: '创业板', value: '2,156.89',change: '-12.34', percent: ''-0.57%', trend: 'down' },'
    { name: '科创50', value: '1,234.56',change: '+45.67', percent: ''+3.84%', trend: 'up' },'
    { name: '沪深300', value: '4,567.89',change: '+78.90', percent: ''+1.76%', trend: 'up' }
  ]
'
 ''// 最新资讯
  const newsData = [
    { title: A股市场今日表现强劲，科技股领涨, time: '10分钟前', type: 'market' },'
    { title: '央行宣布降准0.25个百分点', time: '30分钟前', type: 'policy' },'
    { title: '新能源板块持续活跃，多只个股涨停', time: '1小时前', type: 'sector' },'
    { title: '外资持续流入A股市场', time: '2小时前', type: 'capital' }
  ]

  return (
    <div class={css({ 
     display: 'flex',
      flexDirection: 'column',
     gap: '24px', '
      width: '100%'
    })}>
      {/* 专业投资收益概览 */}
      <div class={css({ 
       display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px'
      })}>
        <For each={investmentData}>
          {(item) => (
            <div class={css({ 
             bg: 'white',
              borderRadius: '16px',
             p: '24px',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
             border: '1px solid #f0f0f0',
             position: 'relative',
             overflow: 'hidden',
             'transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              _hover: { 
                boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
                transform: 'translateY(-4px)
              }
            })}>
              {/* 背景装饰 */}
              <div class={css({ 
               position', absolute: '$4',
                top: 0,
                right: 0,'
               width: '100px',
               height: '100px',
                background: item.bg, '
                borderRadius: '50%',
               'transform: translate(30px, -30px)',
                opacity: 0.1'
              })}/>

              <div class={css({ 
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
               mb: '16px',
               position: 'relative',
                zIndex: 1
              })}>
                <span class={css({ 
                  fontSize: '14px',
                 color: '#8c8c8c',
                  fontWeight: '500'
                })}>
                  {item.title}
                </span>
                <div class={css({ 
                 width: '40px',
                 height: '40px',
                  borderRadius: '12px',
                  background: item.bg,'
                 display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '18px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)
                })}>
                  {item.icon}
                </div>
              </div>

              <div class={css({ 
                fontSize', 32px: '$4',
                fontWeight: '700',
               color: '#262626',
               mb: '12px',
               position: 'relative',
                zIndex: 1
              })}>
                {item.value}
              </div>

              <div class={css({ 
               display: 'flex',
                alignItems: 'center',
               gap: '12px',
               position: 'relative',
                zIndex: 1
              })}>
                <span class={css({ 
                  fontSize: '14px',
                  color : item.change.startsWith('+') ? '#52c41a: '#ff4d4f',
                  fontWeight: '600'
                })}>
                  {item.change}
                </span>
                <span class={css({ 
                  fontSize: '12px',
                  color: item.changePercent.startsWith('+') ? '#52c41a: '#ff4d4f',
                  bg: item.changePercent.startsWith('+') ? '#f6ffed: '#fff2f0',
                 px: '8px',
                 py: '4px',
                  borderRadius: '6px',
                  fontWeight: '500'
                })}>
                  {item.changePercent}
                </span>
              </div>
            </div>
          )}
        </For>
      </div>

      {/* 市场行情和资讯 */}
      <div class={css({ 
       display: 'grid',
        gridTemplateColumns: '1fr 1fr',
        gap: '24px'
      })}>
        {/* 今日行情 */}
        <div class={css({ 
         bg: 'white',
          borderRadius: '16px',
         p: '24px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({ 
           display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: '20px'
          })}>
            <h3 class={css({ 
              fontSize: '18px',
              fontWeight: '600',
             color: '#262626',
              margin: 0
            })}>
              今日行情
            </h3>
            <span class={css({ 
              fontSize: '12px',
             color: '#8c8c8c',
             bg: '#f5f5f5',
             px: '8px',
             py: '4px',
              borderRadius: '4px'
            })}>
              实时更新
            </span>
          </div>
'
          <div class={css({ display: 'flex', flexDirection: 'column', gap: '12px' })}>
            <For each={marketData}>
              {(item) => (
                <div class={css({ 
                 display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                 p: '12px',
                  borderRadius: '8px',
                 bg: '#fafafa',
                 transition: 'all 0.2s ease',
                  _hover: { bg: '#f0f0f0' }
                })}>
                  <div class={css({ 
                   display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  })}>
                    <div class={css({ 
                     width: '8px',
                     height: '8px', '
                      borderRadius: '50%',
                      bg : item.trend ==='up' ? '#52c41a: '#ff4d4f'
                    })}/>
                    <span class={css({ 
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#262626'
                    })}>
                      {item.name}
                    </span>
                  </div>

                  <div class={css({ 
                   display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  })}>
                    <span class={css({ 
                      fontSize: '14px',
                      fontWeight: '600',
                      color: '#262626'
                    })}>
                      {item.value}
                    </span>
                    <span class={css({ 
                      fontSize: '12px',
                      color : item.trend ==='up' ? '#52c41a: '#ff4d4f',
                      bg : item.trend ==='up' ? '#f6ffed: '#fff2f0',
                     px: '6px',
                     py: '2px',
                      borderRadius: '4px'
                    })}>
                      {item.percent}
                    </span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>

        {/* 最新资讯 */}
        <div class={css({ 
         bg: 'white',
          borderRadius: '16px',
         p: '24px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: '1px solid #f0f0f0'
        })}>
          <div class={css({ 
           display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: '20px'
          })}>
            <h3 class={css({ 
              fontSize: '18px',
              fontWeight: '600',
             color: '#262626',
              margin: 0
            })}>
              最新资讯
            </h3>
            <button type="button" class={css({ 
              fontSize: '12px',
             color: '#1890ff',
             bg: 'transparent',
             border: 'none',
             cursor: 'pointer',
              _hover: { textDecoration: 'underline' }
            })}>
              查看更多
            </button>
          </div>
'
          <div class={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
            <For each={newsData}>
              {(item) => (
                <div class={css({ 
                 p: '12px',
                  borderRadius: '8px',
                 bg: '#fafafa',
                 transition: 'all 0.2s ease',
                 cursor: 'pointer',
                  _hover: { bg: '#f0f0f0' }
                })}>
                  <div class={css({ 
                    fontSize: '14px',
                    fontWeight: '500',
                   color: '#262626',
                   mb: '8px',
                    lineHeight: '1.4'
                  })}>
                    {item.title}
                  </div>
                  <div class={css({ 
                   display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  })}>
                    <span class={css({ 
                      fontSize: '12px',
                      color: '#8c8c8c'
                    })}>
                      {item.time}
                    </span>
                    <span class={css({ 
                      fontSize: '10px',
                     color: '#1890ff',
                     bg: '#e6f7ff',
                     px: '6px',
                     py: '2px',
                      borderRadius: '4px'
                    })}>
                      {item.type}
                    </span>
                  </div>
                </div>
              )}
            </For>
          </div>
        </div>
      </div>

      {/* 时间显示 */}
      <div class={css({ 
       textAlign: 'center',
        fontSize: '12px',
       color: '#8c8c8c',
        mt: '16px'
      })}>
        当前时间' : {currentTime()}
      </div>
    </div>
  )
}