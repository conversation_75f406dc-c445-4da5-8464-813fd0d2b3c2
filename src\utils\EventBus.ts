/**
 * 通用事件总线系统
 * 基于@solid-primitives/event-bus的理念实现
 */

export type EventHandler<T = any> = (data: T) => void;
export type UnsubscribeFunction = () => void;

export interface EventBusOptions {
  maxListeners?: number;
  enableLogging?: boolean;
  enableMetrics?: boolean;
}

export interface EventMetrics {
  eventName: string;
  emitCount: number;
  listenerCount: number;
  lastEmitted: number;
  averageProcessingTime: number;
}

/**
 * 类型安全的事件总线
 */
export class EventBus<TEventMap extends Record<string, any> = Record<string, any>> {
  private listeners: Map<keyof TEventMap, Set<EventHandler<TEventMap[keyof TEventMap]>>> = new Map();
  private onceListeners: Map<keyof TEventMap, Set<EventHandler<TEventMap[keyof TEventMap]>>> = new Map();
  private metrics: Map<keyof TEventMap, EventMetrics> = new Map();
  private options: Required<EventBusOptions>;

  constructor(options: EventBusOptions = {}) {
    this.options = {
      maxListeners: options.maxListeners ?? 100,
      enableLogging: options.enableLogging ?? false,
      enableMetrics: options.enableMetrics ?? false
    };
  }

  /**
   * 订阅事件
   */
  on<K extends keyof TEventMap>(
    event: K,
    handler: EventHandler<TEventMap[K]>
  ): UnsubscribeFunction {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }

    const eventListeners = this.listeners.get(event)!;
    
    // 检查监听器数量限制
    if (eventListeners.size >= this.options.maxListeners) {
      console.warn(`EventBus: Maximum listeners (${this.options.maxListeners}) reached for event "${String(event)}"`);
    }

    eventListeners.add(handler);

    if (this.options.enableLogging) {
      console.log(`EventBus: Subscribed to event "${String(event)}", total listeners: ${eventListeners.size}`);
    }

    // 更新指标
    this.updateMetrics(event, subscribe);

    // 返回取消订阅函数
    return () => {
      eventListeners.delete(handler);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
      
      if (this.options.enableLogging) {
        console.log(`EventBus: Unsubscribed from event "${String(event)}"`);
      }
    };
  }

  /**
   * 订阅事件（仅触发一次）
   */
  once<K extends keyof TEventMap>(
    event: K,
    handler: EventHandler<TEventMap[K]>
  ): UnsubscribeFunction {
    if (!this.onceListeners.has(event)) {
      this.onceListeners.set(event, new Set());
    }

    const onceEventListeners = this.onceListeners.get(event)!;
    onceEventListeners.add(handler);

    if (this.options.enableLogging) {
      console.log(`EventBus: Subscribed to event "${String(event)}" (once), total once listeners: ${onceEventListeners.size}`);
    }

    // 返回取消订阅函数
    return () => {
      onceEventListeners.delete(handler);
      if (onceEventListeners.size === 0) {
        this.onceListeners.delete(event);
      }
    };
  }

  /**
   * 发布事件
   */
  emit<K extends keyof TEventMap>(event: K, data: TEventMap[K]): void {
    const startTime = performance.now();
    
    if (this.options.enableLogging) {
      console.log(`EventBus: Emitting event "${String(event)}"`, data);
    }

    // 触发普通监听器
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`EventBus: Error in event handler for "${String(event)}":`, error);
        }
      });
    }

    // 触发一次性监听器
    const onceEventListeners = this.onceListeners.get(event);
    if (onceEventListeners) {
      onceEventListeners.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`EventBus: Error in once event handler for "${String(event)}:`, error);
        }
      });
      // 清除一次性监听器
      this.onceListeners.delete(event);
    }

    // 更新指标
    if (this.options.enableMetrics) {
      const processingTime = performance.now() - startTime;
      this.updateMetrics(event, emit, processingTime);
    }
  }

  /**
   * 异步发布事件
   */
  async emitAsync<K extends keyof TEventMap>(event: K, data: TEventMap[K]): Promise<void> {
    const startTime = performance.now();
    
    if (this.options.enableLogging) {
      console.log(`EventBus: Emitting async event "${String(event)}"`, data);
    }

    const promises: Promise<void>[] = [];

    // 触发普通监听器
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(handler => {
        promises.push(
          Promise.resolve().then(() => handler(data)).catch(error => {
            console.error(`EventBus: Error in async event handler for "${String(event)}":`, error);
          })
        );
      });
    }

    // 触发一次性监听器
    const onceEventListeners = this.onceListeners.get(event);
    if (onceEventListeners) {
      onceEventListeners.forEach(handler => {
        promises.push(
          Promise.resolve().then(() => handler(data)).catch(error => {
            console.error(`EventBus: Error in async once event handler for "${String(event)}:`, error);
          })
        );
      });
      // 清除一次性监听器
      this.onceListeners.delete(event);
    }

    await Promise.all(promises);

    // 更新指标
    if (this.options.enableMetrics) {
      const processingTime = performance.now() - startTime;
      this.updateMetrics(event, emit, processingTime);
    }
  }

  /**
   * 取消订阅指定事件的所有监听器
   */
  off<K extends keyof TEventMap>(event: K, handler?: EventHandler<TEventMap[K]>): void {
    if (!handler) {
      // 移除所有监听器
      this.listeners.delete(event);
      this.onceListeners.delete(event);
      
      if (this.options.enableLogging) {
        console.log(`EventBus: Removed all listeners for event "${String(event)}"`);
      }
    } else {
      // 移除特定监听器
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(handler);
        if (eventListeners.size === 0) {
          this.listeners.delete(event);
        }
      }

      const onceEventListeners = this.onceListeners.get(event);
      if (onceEventListeners) {
        onceEventListeners.delete(handler);
        if (onceEventListeners.size === 0) {
          this.onceListeners.delete(event);
        }
      }
      
      if (this.options.enableLogging) {
        console.log(`EventBus: Removed specific listener for event '${String(event)}`);
      }
    }
  }

  /**
   * 获取事件的监听器数量
   */
  listenerCount<K extends keyof TEventMap>(event: K): number {
    const regularCount = this.listeners.get(event)?.size ?? 0;
    const onceCount = this.onceListeners.get(event)?.size ?? 0;
    return regularCount + onceCount;
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): (keyof TEventMap)[] {
    const events = new Set<keyof TEventMap>();
    this.listeners.forEach((_, event) => events.add(event));
    this.onceListeners.forEach((_, event) => events.add(event));
    return Array.from(events);
  }

  /**
   * 清除所有监听器
   */
  clear(): void {
    this.listeners.clear();
    this.onceListeners.clear();
    this.metrics.clear();
    
    if (this.options.enableLogging) {
      console.log(EventBus, Cleared all listeners);
    }
  }

  /**
   * 获取事件指标
   */
  getMetrics<K extends keyof TEventMap>(event?: K): EventMetrics | EventMetrics[] {
    if (event) {
      return this.metrics.get(event) || {
        eventName: String(event),
        emitCount: 0,
        listenerCount: this.listenerCount(event),
        lastEmitted: 0,
        averageProcessingTime: 0
      };
    }
    
    return Array.from(this.metrics.values());
  }

  /**
   * 更新事件指标
   */
  private updateMetrics<K extends keyof TEventMap>(
    event: K,'
    action: 'subscribe' | 'emit',
    processingTime?: number
  ): void {
    if (!this.options.enableMetrics) return;

    let metrics = this.metrics.get(event);
    if (!metrics) {
      metrics = {
        eventName: String(event),
        emitCount: 0,
        listenerCount: 0,
        lastEmitted: 0,
        averageProcessingTime: 0
      };
      this.metrics.set(event, metrics);
    }
'
    if (action === 'emit') {
      metrics.emitCount++;
      metrics.lastEmitted = Date.now();
      
      if (processingTime !== undefined) {
        // 计算平均处理时间
        metrics.averageProcessingTime = 
          (metrics.averageProcessingTime * (metrics.emitCount - 1) + processingTime) / metrics.emitCount;
      }
    }

    metrics.listenerCount = this.listenerCount(event);
  }

  /**
   * 创建命名空间事件总线
   */
  namespace<TNamespaceEventMap extends Record<string, any>>(
    prefix: string
  ): EventBus<TNamespaceEventMap> {
    const namespacedBus = new EventBus<TNamespaceEventMap>(this.options);
    
    // 代理所有方法到主总线，添加前缀
    const originalOn = namespacedBus.on.bind(namespacedBus);
    namespacedBus.on = <K extends keyof TNamespaceEventMap>(
      event: K,
      handler: EventHandler<TNamespaceEventMap[K]>
    ) => {
      const namespacedEvent = `${prefix}:${String(event)}` as keyof TEventMap;
      return this.on(namespacedEvent, handler as any);
    };

    const originalEmit = namespacedBus.emit.bind(namespacedBus);
    namespacedBus.emit = <K extends keyof TNamespaceEventMap>(
      event: K,
      data: TNamespaceEventMap[K]
    ) => {
      const namespacedEvent = `${prefix}:${String(event)}` as keyof TEventMap;
      return this.emit(namespacedEvent, data as any);
    };

    return namespacedBus;
  }
}

/**
 * 创建全局事件总线实例
 */
export const globalEventBus = new EventBus({
  maxListeners: 1000,
  enableLogging: process.env.NODE_ENV === development,
  enableMetrics: true
});

/**
 * 便捷的事件总线钩子
 */
export function createEventBus<TEventMap extends Record<string, any>>(
  options?: EventBusOptions
): EventBus<TEventMap> {
  return new EventBus<TEventMap>(options);
}

/**
 * 事件总线装饰器（用于类方法）
 */
export function eventHandler<TEventMap extends Record<string, any>>(
  eventBus: EventBus<TEventMap>,
  event: keyof TEventMap
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    // 在类实例化时自动订阅事件
    const originalConstructor = target.constructor;
    target.constructor = function (...args: any[]) {
      const instance = originalConstructor.apply(this, args);
      eventBus.on(event, originalMethod.bind(instance));
      return instance;
    };
    
    return descriptor;
  };
}

export default EventBus;