import { lazy, Suspense } from 'solid-js';
import { Router, Route } from '@solidjs/router';
import SimpleLayout from './components/SimpleLayout';
import HopeProvider from './components/HopeProvider';

// 懒加载页面组件以优化性能
const Dashboard = lazy(() => import(./pages/SimpleDashboard'));
const EnhancedDashboard = lazy(() => import('./pages/EnhancedDashboard'));
const StrategyEditor = lazy(() => import('./pages/StrategyEditor'));
const BacktestAnalysis = lazy(() => import('./pages/BacktestAnalysis'));
const ParameterOptimization = lazy(() => import('./pages/ParameterOptimization'));
const MarketData = lazy(() => import('./pages/MarketData'));

// 简单的测试组件
function TestComponent() {
  return (
    <div style={{
      padding: 20px',
      background: 'white',
      borderRadius: '8px',
      margin: '20px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <h1 style={{ color: '#1890ff', marginBottom: '16px' }}>
        🚀 新界面测试成功！
      </h1>
      <p style={{ color: '#666', fontSize: '16px' }}>
        如果您看到这个消息，说明新的应用架构已经正常工作了！
      </p>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginTop: '20px'
      }}>
        <div style={{
          padding: '16px',
          background: '#f0f9ff',
          borderRadius: '8px',
          border: '1px solid #0ea5e9'
        }}>
          <h3 style={{ margin: '0 0 8px 0', color: '#0ea5e9' }}>✅ SolidJS</h3>
          <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>框架正常运行</p>
        </div>
        <div style={{
          padding: '16px',
          background: '#f0fdf4',
          borderRadius: '8px',
          border: '1px solid #22c55e'
        }}>
          <h3 style={{ margin: '0 0 8px 0', color: '#22c55e' }}>✅ 路由</h3>
          <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>路由系统正常</p>
        </div>
        <div style={{
          padding: '16px',
          background: '#fefce8',
          borderRadius: '8px',
          border: '1px solid #eab308'
        }}>
          <h3 style={{ margin: '0 0 8px 0', color: '#eab308' }}>✅ 布局</h3>
          <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>布局组件正常</p>
        </div>
      </div>
    </div>
  );
}

export default function App() {
  return (
    <HopeProvider>
      <Router root={SimpleLayout}>
        <Route path="/" component={EnhancedDashboard} />
        <Route path="/simple" component={Dashboard} />
        <Route path="/test" component={TestComponent} />
        <Route path="/strategy" component={StrategyEditor} />
        <Route path="/backtest" component={BacktestAnalysis} />
        <Route path="/optimization" component={ParameterOptimization} />
        <Route path="/market" component={MarketData} />
      </Router>
    </HopeProvider>
  );
}