import { createSignal, JSX, Show, For } from solid-js'''
import { A, useLocation } from '@solidjs/router'''
import { css } from '../../styled-system/css''
interface AdvancedLayoutProps {
  children: JSX.Element
}

export default function AdvancedLayout(props: AdvancedLayoutProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = createSignal(false)
  const [expandedGroups, setExpandedGroups] = createSignal({
    market: true,
    trading: false,
    strategy: false'
  })
  const toggleGroup = (key: market' |trading' |strategy') => {
    setExpandedGroups(prev => ({ ...prev, [key]: !prev[key] }))
  }
  const location = useLocation()
''
  // 专业导航配置 - 完全匹配 a/ 项目'''
  const navigation = ['''
    { name: 仪表盘, href:  /, icon:  📊, type:  single, color: '#1890ff }, '''
    { name:  行情中心, href:  /market, icon:  📈, type:  single, color: '#52c41a }, '''
    { name:  交易中心, href:  /trading, icon:  💰, type:  single, color: '#fa8c16 }, '''
    { name:  策略中心, href:  /strategy, icon:  🧠, type:  single, color: '#722ed1 }, '''
    { name:  回测分析, href:  /backtest, icon:  🔄, type:  single, color: '#13c2c2 }, '''
    { name:  参数优化, href:  /optimization, icon:  🔧, type:  single, color: '#fa541c }, '''
    { name:  投资组合, href:  /portfolio, icon:  📋, type:  single, color: '#eb2f96 }, '''
    { name:  风险管理, href:  /risk, icon:  🛡️, type:  single, color: '#f5222d }, '''
    { name:  系统设置, href:  /settings, icon:  ⚙️, type:  single, color: '#666 }
  ]

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed())
  }

  // 获取页面标题
  const getPageTitle = () => {''
    const titleMap: Record<string, string> = { '''
      /: 投资收益盘,
      /market: 行情中心,
      /trading: '交易中心,
      /strategy: '策略中心,
      /backtest: '回测分析,
      /optimization: '参数优化,
      /portfolio: '投资组合,
      /risk: '风险管理,
      /settings: ''系统设置
    }
    return titleMap[location.pathname] || ''量化平台'
  }

  // 获取面包屑导航''
  const getBreadcrumbs = () => {'''
    const path = location.pathname''''
    if (path ===/) return ['首页,投资收益盘']
    if (path ==='/market') return ['首页,行情中心']
    if (path ==='/trading') return ['首页,交易中心']
    if (path ==='/strategy') return ['首页,策略中心']
    if (path ==='/backtest') return ['首页,回测分析']
    if (path ==='/portfolio') return ['首页,投资组合']
    if (path ==='/risk') return ['首页,风险管理']
    if (path ==='/settings') return ['首页,系统设置']
    return ['首页', getPageTitle()]
  }

  return (''
    <div class={css({ '''
      display: flex,
      height: '100vh,
      bg: ''#f5f7fa
    })}>
      {/* 左侧导航栏 */}
      <aside class={css({ '''
        width: isSidebarCollapsed() ? 64px: '240px,
        bg: white,
        borderRight: '1px solid #e8e8e8,
        display: flex,
        flexDirection: column,
        transition: width 0.3s ease,
        boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1),'
        position:  relative,
        zIndex: 10
      })}>
        {/* Logo区域 */}
        <div class={css({ '''
          height: '64px,
          display: flex,
          alignItems: center,
          px: '16px,
          borderBottom: ''1px solid #e8e8e8
        })}>''
          <div class={css({ '''
            display: flex,
            alignItems: center,
            gap: ''12px
          })}>''
            <div class={css({ '''
              width: '32px,
              height: '32px,
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%), ''
              color:  white,
              borderRadius: '8px,
              display: flex,
              alignItems: center,
              justifyContent: center,
              fontWeight: bold,
              fontSize: ''18px
            })}>
              V
            </div>
            <Show when={!isSidebarCollapsed()}>''
              <span class={css({ '''
                fontSize: '16px,
                fontWeight: '600,
                color: ''#333
              })}>
                量化平台
              </span>
            </Show>
          </div>
        </div>
''
        {/* 导航菜单 */}
        <nav class={css({''''
          flex: 1,
         py: '16px,
          overflowY: auto
        })}>'''
          <div class={css({ px: '8px })}>''
            <For each={navigation}>'''
              {(item) => ('''
                <Show''''
                  when={item.type ===single'}
                  fallback={ '''
                    <div class={css({ mb: '4px })}>'
                      {/* 分组标题 */}
                      <div''
                        class={css({ '''
                         display: flex,
                          alignItems: center,
                         px: '12px,
                         py: '8px,
                         mx: '4px,
                          borderRadius: '6px,
                         cursor: pointer,
                          fontSize: '14px,
                          fontWeight: '500,
                         color: '#666,
                          _hover: { bg: '#f5f5f5 }
                        })}
                        onClick={() => toggleGroup(item.key!)}
                      >'''
                        <span class={css({ mr:  8px, fontSize: '16px })}>{item.icon}</span>'
                        <Show when={!isSidebarCollapsed()}>
                          <span class={css({ flex: 1 })}>{item.name}</span>''
                          <span class={css({ '''
                            fontSize: '12px,
                            transform: expandedGroups()[item.key!] ? rotate(180deg): rotate(0deg),
                            transition: transform 0.2s ease
                          })}>
                            ▼
                          </span>
                        </Show>
                      </div>
                      {/* 子菜单 */}
                      <Show when={!isSidebarCollapsed() && expandedGroups()[item.key!]}>'''
                        <div class={css({ ml:  20px, mt: '4px })}>'
                          <For each={item.children}>
                            {(child) => (
                              <A
                                href={child.href}
                                class={css({ '''
                                 display: block,
                                 px: '12px,
                                 py: '6px,
                                 mx: '4px,
                                  borderRadius: '4px,
                                  fontSize: '13px,
                                  color: location.pathname === child.href ? '#1890ff: '#666,
                                  bg: location.pathname === child.href ? '#e6f7ff: transparent,
                                  textDecoration: none,
                                  _hover: { bg: location.pathname === child.href ? '#e6f7ff: '#f5f5f5 }
                                })}
                              >
                                {child.name}
                              </A>
                            )}
                          </For>
                        </div>
                      </Show>
                    </div>
                  }
                >
                  {/* 单个导航项 */}
                  <A
                    href={item.href!}
                    class={css({ '''
                     display: flex,
                      alignItems: center,
                     px: '12px,
                     py: '10px,
                     mx: '4px,
                     mb: '4px,
                      borderRadius: '6px,
                      fontSize: '14px,
                      fontWeight: '500,
                      color: location.pathname === item.href ? '#1890ff: '#666,
                      bg: location.pathname === item.href ? '#e6f7ff: transparent,
                      textDecoration: none,
                      _hover: { bg: location.pathname === item.href ? '#e6f7ff: '#f5f5f5 }
                    })}
                  >'''
                    <span class={css({ mr:  8px, fontSize: '16px })}>{item.icon}</span>'
                    <Show when={!isSidebarCollapsed()}>
                      <span>{item.name}</span>
                    </Show>
                  </A>
                </Show>
              )}
            </For>
          </div>
        </nav>

        {/* 侧边栏底部 */}
        <div class={css({ '''
         p: '16px,
          borderTop: ''1px solid #e8e8e8
        })}>'''
          <button''''
            type=button'''
            onClick={toggleSidebar}
            class={css({'''''
             width: '100%,
             p: '8px,
             border: none,
             bg: transparent,
              borderRadius: '6px,
             cursor: pointer,
              fontSize: '16px,
              _hover: { bg: '#f5f5f5 }
            })}
          >'''
            {isSidebarCollapsed() ? '→:'←'}
          </button>
        </div>
      </aside>
''
      {/* 主内容区域 */}
      <div class={css({ flex: 1,display:  flex, flexDirection: column })}>'
        {/* 顶部标题栏 */}
        <header class={css({ '''
         bg: white,
         borderBottom: '1px solid #e8e8e8,
         px: '24px,
          py: ''16px
        })}>''
          <div class={css({ '''
           display: flex,
            justifyContent: space-between,
            alignItems: center
          })}>
            <div>''
              <h1 class={css({ '''
                fontSize: '20px,
                fontWeight: '600,
               color: '#333,
                mb: ''4px
              })}>
                {getPageTitle()}
              </h1>''
              <div class={css({ '''
                fontSize: '12px,
                color: ''#999
              })}>
                <For each={getBreadcrumbs()}>
                  {(item, index) => (
                    <>''
                      <Show when={index() > 0}>'''
                        <span class={css({ mx: '4px })}>/</span>''
                      </Show>'''
                      <span class={css({'''''
                        color: index() === getBreadcrumbs().length - 1 ? '#1890ff: ''#999
                      })}>
                        {item}
                      </span>
                    </>
                  )}
                </For>
              </div>
            </div>
            ''
            <div class={css({ '''
             display: flex,
              alignItems: center,
              gap: ''12px
            })}>'''
              <button type=button" class={css({ """
               p: '8px,
               border: none,
               bg: transparent,
                borderRadius: '6px,
               cursor: pointer,
                fontSize: '16px,
                _hover: { bg: '#f5f5f5 }
              })}>
                🔔''
              </button>'''
              <button type=button" class={css({ """
               p: '8px,
               border: none,
               bg: transparent,
                borderRadius: '6px,
               cursor: pointer,
                fontSize: '16px,
                _hover: { bg: '#f5f5f5 }
              })}>
                ⚙️
              </button>''
              <div class={css({ '''
               width: '32px,
               height: '32px,
               bg: '#1890ff,
               color: white,
                borderRadius: '50%,
               display: flex,
                alignItems: center,
                justifyContent: center,
                fontSize: '14px,
                fontWeight: bold
              })}>
                U
              </div>
            </div>
          </div>
        </header>
''
        {/* 页面内容 */}
        <main class={css({''''
          flex: 1,
         p: '24px,
          overflowY: auto
        })}>
          {props.children}
        </main>
      </div>
    </div>
  )
}
