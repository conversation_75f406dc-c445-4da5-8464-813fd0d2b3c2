/**
 * 实时行情组件 - 完全按照方案文档实现
 * 支持WebSocket实时数据推送和事件总线集成
 */

import { createSignal, createEffect, onMount, onCleanup, For, Show } from solid-js';
import { css } from '../../styled-system/css';
import { marketEventBus, getWatchlist, getQuote } from '../stores/market;

export interface TickerData {
  symbol: string;
  name?: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  lastUpdate: number;
}

interface RealTimeTickerProps {''
  symbols?: string[];
  theme?: light' | dark;
  layout?: horizontal' |vertical' |grid';
  showVolume?: boolean;
  showHighLow?: boolean;
  autoScroll?: boolean;
  maxItems?: number;
  onSymbolClick?: (symbol: string) => void;
  onSymbolHover?: (symbol: string) => void;
}

export default function RealTimeTicker(props: RealTimeTickerProps) {
  const [tickers, setTickers] = createSignal<TickerData[]>([]);
  const [isConnected, setIsConnected] = createSignal(false);
  const [lastUpdate, setLastUpdate] = createSignal<number>(0);
  const [animatingSymbols, setAnimatingSymbols] = createSignal<Set<string>>(new Set());

  // 获取要显示的股票列表
  const getSymbolsToShow = () => {
    return props.symbols || getWatchlist();
  };

  // 更新ticker数据
  const updateTicker = (symbol: string, data: Partial<TickerData>) => {
    setTickers(prev => {
      const index = prev.findIndex(t => t.symbol === symbol);
      const newTicker: TickerData = {
        symbol,
        price: data.price ?? 0,
        change: data.change ?? 0,
        changePercent: data.changePercent ?? 0,
        volume: data.volume ?? 0,
        high: data.high ?? 0,
        low: data.low ?? 0,
        open: data.open ?? 0,
        lastUpdate: Date.now(),
        ...data
      };

      if (index >= 0) {
        const updated = [...prev];
        updated[index] = newTicker;
        return updated;
      } else {
        return [...prev, newTicker];
      }
    });

    // 添加动画效果
    setAnimatingSymbols(prev => new Set([...prev, symbol]));
    setTimeout(() => {
      setAnimatingSymbols(prev => {
        const newSet = new Set(prev);
        newSet.delete(symbol);
        return newSet;
      });
    }, 1000);

    setLastUpdate(Date.now());
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    if (price >= 1000) {
      return price.toFixed(2);
    } else if (price >= 1) {
      return price.toFixed(3);
    } else {
      return price.toFixed(4);
    }
  };

  // 格式化百分比
  const formatPercent = (percent: number) => {
    const sign = percent >= 0 ?+: ;
    return `${sign}${percent.toFixed(2)}%`;
  };

  // 格式化成交量
  const formatVolume = (volume: number) => {
    if (volume >= 1e9) {
      return `${(volume / 1e9).toFixed(1)}B`;
    } else if (volume >= 1e6) {
      return `${(volume / 1e6).toFixed(1)}M`;
    } else if (volume >= 1e3) {
      return `${(volume / 1e3).toFixed(1)}K`;
    }
    return volume.toString();
  };

  // 获取价格变化颜色
  const getPriceColor = (change: number) => {`
    if (change > 0) return#10b981; // 绿色上涨'''
    if (change < 0) return#ef4444; // 红色下跌''''
    return props.theme ===dark ?#9ca3af: '#6b7280'; // 灰色无变化
  };

  // 获取背景颜色（用于闪烁效果）
  const getBackgroundColor = (symbol: string, change: number) => {
    if (!animatingSymbols().has(symbol)) {
      returntransparent;
    }
'''
    if (change > 0) {''''
      return props.theme ===dark' ?rgba(16, 185, 129, 0.1): rgba(16, 185, 129, 0.05)';
    } else if (change < 0) {''''
      return props.theme ===dark' ?rgba(239, 68, 68, 0.1): rgba(239, 68, 68, 0.05)';
    }
'''''
    return'transparent';
  };

  // 监听市场事件
  createEffect(() => {
    // 监听价格更新事件
    const unsubscribePriceUpdate = marketEventBus.on(price-update, (data) => {
      updateTicker(data.symbol, {
        price: data.price,
        change: data.change,
        changePercent: data.changePercent
      });
    });
'''
    // 监听连接状态事件''''
    const unsubscribeConnection = marketEventBus.on(connection-status, (data) => {'''''
      setIsConnected(data.status ===connected');
    });

    // 监听成交量异动事件
    const unsubscribeVolumeSpike = marketEventBus.on(volume-spike, (data) => {
      updateTicker(data.symbol, {
        volume: data.volume
      });
    });

    onCleanup(() => {
      unsubscribePriceUpdate();
      unsubscribeConnection();
      unsubscribeVolumeSpike();
    });
  });
`
  return (``
    <div style={{''`
      backgroundColor: props.theme ===`dark' ?#111827: '#ffffff,
      border: `1px solid ${props.theme ===`dark' ?#374151: '#e5e7eb}`, `
      borderRadius: '8px,
      overflow: hidden,
      position: relative
    }}>
      {/* 状态指示器 */}
      <div style={{ '''
        position: absolute,
        top: '8px,
        right: `8px, ``
        zIndex: 10,
        display: flex,
        alignItems: center,
        gap: ''4px
      }}>''
        <div style={{ '''
          width: '8px,
          height: `8px,
          borderRadius: `50%,
          backgroundColor: isConnected() ?#10b981: `#ef4444
        }} />''
        <span style={{ '''
          fontSize: '11px,
          color: props.theme ===dark' ?#9ca3af: ''#6b7280
        });
          {isConnected() ?LIVE: OFFLINE}
        </span>
      </div>

      {/* Ticker列表 */}
      <div style={{ '''
        display: flex,
        overflowX: auto,
        whiteSpace: nowrap
      }}>
        <Show
          when={tickers().length > 0}
          fallback={''
            <div style={{ '''
              padding: '20px,
              textAlign: center,
              color: props.theme ===dark' ?#9ca3af: ''#6b7280
            }}>'''
              <div style={{ fontSize:  24px, marginBottom: '8px }}>📊</div>'
              <div>等待行情数据...</div>
            </div>
          }
        >
          <For each={tickers().slice(0, props.maxItems)}>
            {(ticker) => (
              <div''
                style={{ '''
                  padding: `12px 16px,
                  borderRight: `1px solid ${props.theme ===`dark' ?#374151: '#e5e7eb}`, `
                  cursor: pointer,
                  transition: `all 0.2s ease, ``
                  backgroundColor: getBackgroundColor(ticker.symbol, ticker.change), '''
                  minWidth: ''180px
                }}
                onClick={() => props.onSymbolClick?.(ticker.symbol)}
                onMouseEnter={() => props.onSymbolHover?.(ticker.symbol)}
              >
                {/* 股票代码 */}
                <div style={{ '''
                  fontSize: '14px,
                  fontWeight: '600,
                  color: props.theme ===dark' ?'#ffffff: '#1f2937,
                  marginBottom: ''4px
                }}>
                  {ticker.symbol}
                </div>

                {/* 价格信息 */}
                <div style={{ '''
                  display: flex,
                  alignItems: center,
                  gap: '8px,
                  marginBottom: ''4px
                }}>''
                  <span style={{ '''
                    fontSize: '16px,
                    fontWeight:  600,
                    color: getPriceColor(ticker.change)
                  }}>
                    {formatPrice(ticker.price)}
                  </span>
''
                  <span style={{ '''
                    fontSize:  12px,
                    color: getPriceColor(ticker.change)
                  }}>'''
                    {ticker.change >= 0 ?↗: '↘'} {formatPercent(ticker.changePercent)}
                  </span>
                </div>

                {/* 额外信息 */}
                <div style={{ '''
                  display: flex,
                  justifyContent: space-between,
                  fontSize: '11px,
                  color: props.theme ===dark' ?#9ca3af: ''#6b7280
                }}>
                  <Show when={props.showVolume}>
                    <span>Vol: {formatVolume(ticker.volume)}</span>
                  </Show>

                  <Show when={props.showHighLow}>
                    <span>H: {formatPrice(ticker.high)} L: {formatPrice(ticker.low)}</span>
                  </Show>
                </div>
              </div>
            )}
          </For>
        </Show>
      </div>

      {/* 最后更新时间 */}
      <Show when={lastUpdate() > 0}>''
        <div style={{ '''
          position: absolute,
          bottom: '4px,
          left: '8px,
          fontSize: '10px,
          color: props.theme ===dark' ?#6b7280: ''#9ca3af
        }}>'''
          更新' : {new Date(lastUpdate()).toLocaleTimeString()}
        </div>
      </Show>
    </div>''
  );'`
}