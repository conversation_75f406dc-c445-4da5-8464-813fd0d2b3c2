import { JSX } from 'solid-js';
import { A } from '@solidjs/router';
import { 
  Box, 
  Container, 
  Flex, 
  HStack, 
  VStack,
  Heading, 
  Text, 
  Button,
  Badge,
  Spacer
} from '@hope-ui/solid';

interface EnhancedLayoutProps {
  children: JSX.Element;
}

export default function EnhancedLayout(props: EnhancedLayoutProps) {
  return (
    <Flex direction="column" minH="100vh" bg="neutral.50">
      {/* 顶部导航栏 */}
      <Box
        as='header'
        bg='white'
        borderBottom='1px solid'
        borderColor='neutral.200'
        shadow='sm'
        position='sticky'
        top={0}
        zIndex={10}
      >
        <Container maxW="1200px" px={6} py={4}>
          <Flex align="center" justify="space-between">
            {/* Logo和标题 */}
            <HStack spacing={3}>
              <Heading size="lg" color="primary.600">
                📊 量化交易平台
              </Heading>
              <Badge colorScheme="primary" variant="subtle" size="sm">
                SolidJS
              </Badge>
            </HStack>

            {/* 导航菜单 */}
            <HStack spacing={6}>
              <A href="/">
                <Button variant="ghost" colorScheme="neutral" size="sm">
                  仪表盘
                </Button>
              </A>
              <A href="/strategy">
                <Button variant="ghost" colorScheme="neutral" size="sm">
                  策略编辑
                </Button>
              </A>
              <A href="/backtest">
                <Button variant="ghost" colorScheme="neutral" size="sm">
                  回测分析
                </Button>
              </A>
              <A href="/market">
                <Button variant="ghost" colorScheme="neutral" size="sm">
                  市场数据
                </Button>
              </A>
              <A href="/test">
                <Button variant="ghost" colorScheme="neutral" size="sm">
                  测试页面
                </Button>
              </A>
            </HStack>
          </Flex>
        </Container>
      </Box>

      {/* 主内容区域 */}
      <Box as="main" flex={1}>
        <Container maxW="1200px" px={6} py={8}>
          {props.children}
        </Container>
      </Box>

      {/* 底部 */}
      <Box
        as='footer'
        bg='white'
        borderTop='1px solid'
        borderColor='neutral.200'
        py={6}
      >
        <Container maxW="1200px" px={6}>
          <Flex align="center" justify="center">
            <Text size="sm" color="neutral.600">
              © 2024 量化交易前端平台 - 基于 SolidJS + Hope UI + Panda CSS 构建
            </Text>
          </Flex>
        </Container>
      </Box>
    </Flex>
  );
}