/**
 * 国际化配置
 */

export type Language = 'zh-CN' | 'en-US';

export interface I18nMessages {
  [key: string]: string | I18nMessages;
}

// 中文翻译
const zhCN: I18nMessages = {
  common: { 
    loading: 加载中...,
    error: 错误',
    success: '成功',
    warning: '警告',
    info: '信息',
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    refresh: '刷新',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    reset: '重置',
    close: '关闭',
    open: '打开',
    yes: '是',
    no: '否'
  },
  auth: { 
    login: '登录',
    logout: '退出登录',
    register: '注册',
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码',
    email: '邮箱',
    forgotPassword: '忘记密码',
    rememberMe: '记住我',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    logoutSuccess: '退出成功',
    invalidCredentials: '用户名或密码错误',
    slideToVerify: '向右滑动完成验证',
    verifySuccess: '验证成功',
    verifyFailed: '验证失败，请重试'
  },
  navigation: { 
    dashboard: '仪表盘',
    market: '市场数据',
    strategy: '策略管理',
    backtest: '回测分析',
    trading: '交易',
    portfolio: '投资组合',
    risk: '风险管理',
    settings: '设置',
    profile: '个人资料'
  },
  market: { 
    symbol: '代码',
    name: '名称',
    price: '价格',
    change: '涨跌',
    changePercent: '涨跌幅',
    volume: '成交量',
    high: '最高',
    low: '最低',
    open: '开盘',
    close: '收盘',
    marketCap: '市值',
    pe: '市盈率',
    watchlist: '自选股',
    addToWatchlist: '加入自选',
    removeFromWatchlist: '移出自选'
  },
  strategy: { 
    strategies: '策略',
    createStrategy: '创建策略',
    editStrategy: '编辑策略',
    deleteStrategy: '删除策略',
    strategyName: '策略名称',
    strategyDescription: '策略描述',
    strategyType: '策略类型',
    strategyStatus: '策略状态',
    strategyCode: '策略代码',
    parameters: '参数',
    performance: '表现',
    totalReturn: '总收益',
    sharpeRatio: '夏普比率',
    maxDrawdown: '最大回撤',
    winRate: '胜率',
    startStrategy: '启动策略',
    stopStrategy: '停止策略',
    pauseStrategy: '暂停策略'
  },
  backtest: { 
    backtest: '回测',
    createBacktest: '创建回测',
    backtestResults: '回测结果',
    startDate: '开始日期',
    endDate: '结束日期',
    initialCapital: '初始资金',
    finalCapital: '最终资金',
    trades: '交易次数',
    runBacktest: '运行回测',
    backtestRunning: '回测运行中',
    backtestCompleted: '回测完成',
    backtestFailed: '回测失败'
  },
  chart: { 
    indicators: '技术指标',
    timeframe: '时间周期',
    candlestick: 'K线图',
    line: '线图',
    area: '面积图',
    volume: '成交量',
    ma: '移动平均线',
    ema: '指数移动平均线',
    macd: 'MACD',
    rsi: 'RSI',
    bollinger: '布林带',
    kd: 'KD指标'
  },
  error: { 
    networkError: '网络错误',
    serverError: '服务器错误',
    unauthorized: '未授权访问',
    forbidden: '访问被拒绝',
    notFound: '页面未找到',
    validationError: '验证错误',
    unknownError: '未知错误'
  }
};

// 英文翻译
const enUS: I18nMessages = {
  common: { 
    loading: Loading...,
    error: Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info',
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    refresh: 'Refresh',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    close: 'Close',
    open: 'Open',
    yes: 'Yes',
    no: 'No'
  },
  auth: { 
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    username: 'Username',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    email: 'Email',
    forgotPassword: 'Forgot Password',
    rememberMe: 'Remember Me',
    loginSuccess: 'Login Successful',
    loginFailed: 'Login Failed',
    logoutSuccess: 'Logout Successful',
    invalidCredentials: 'Invalid username or password',
    slideToVerify: 'Slide to verify',
    verifySuccess: 'Verification successful',
    verifyFailed: 'Verification failed, please try again'
  },
  navigation: { 
    dashboard: 'Dashboard',
    market: 'Market Data',
    strategy: 'Strategy',
    backtest: 'Backtest',
    trading: 'Trading',
    portfolio: 'Portfolio',
    risk: 'Risk Management',
    settings: 'Settings',
    profile: 'Profile'
  },
  market: { 
    symbol: 'Symbol',
    name: 'Name',
    price: 'Price',
    change: 'Change',
    changePercent: 'Change %',
    volume: 'Volume',
    high: 'High',
    low: 'Low',
    open: 'Open',
    close: 'Close',
    marketCap: 'Market Cap',
    pe: 'P/E Ratio',
    watchlist: 'Watchlist',
    addToWatchlist: 'Add to Watchlist',
    removeFromWatchlist: 'Remove from Watchlist'
  },
  strategy: { 
    strategies: 'Strategies',
    createStrategy: 'Create Strategy',
    editStrategy: 'Edit Strategy',
    deleteStrategy: 'Delete Strategy',
    strategyName: 'Strategy Name',
    strategyDescription: 'Strategy Description',
    strategyType: 'Strategy Type',
    strategyStatus: 'Strategy Status',
    strategyCode: 'Strategy Code',
    parameters: 'Parameters',
    performance: 'Performance',
    totalReturn: 'Total Return',
    sharpeRatio: 'Sharpe Ratio',
    maxDrawdown: 'Max Drawdown',
    winRate: 'Win Rate',
    startStrategy: 'Start Strategy',
    stopStrategy: 'Stop Strategy',
    pauseStrategy: 'Pause Strategy'
  },
  backtest: { 
    backtest: 'Backtest',
    createBacktest: 'Create Backtest',
    backtestResults: 'Backtest Results',
    startDate: 'Start Date',
    endDate: 'End Date',
    initialCapital: 'Initial Capital',
    finalCapital: 'Final Capital',
    trades: 'Trades',
    runBacktest: 'Run Backtest',
    backtestRunning: 'Backtest Running',
    backtestCompleted: 'Backtest Completed',
    backtestFailed: 'Backtest Failed'
  },
  chart: { 
    indicators: 'Indicators',
    timeframe: 'Timeframe',
    candlestick: 'Candlestick',
    line: 'Line',
    area: 'Area',
    volume: 'Volume',
    ma: 'Moving Average',
    ema: 'Exponential Moving Average',
    macd: 'MACD',
    rsi: 'RSI',
    bollinger: 'Bollinger Bands',
    kd: 'KD Indicator'
  },
  error: { 
    networkError: 'Network Error',
    serverError: 'Server Error',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    notFound: 'Not Found',
    validationError: 'Validation Error',
    unknownError: 'Unknown Error'
  }
};

const messages = { 
  zh-CN: 'zhCN',
  en-US: 'enUS' };

export default messages;