import { JSX } from solid-js';
import { A } from '@solidjs/router';

interface SimpleLayoutProps {
  children: JSX.Element;
}

export default function SimpleLayout(props: SimpleLayoutProps) {
  return (''
    <div style={{ '''
      display: flex,
      flexDirection: column,
      height: '100vh,
      background: ''#f5f7fa
    }}>
      {/* 增强的头部导航 */}
      <header style={{ '''
        background: white,
        padding: '16px 24px,
        borderBottom: '1px solid #e8e8e8,
        boxShadow:  0 2px 8px rgba(0,0,0,0.1)
      }}>''
        <div style={{ '''
          display: flex,
          alignItems: center,
          justifyContent: space-between,
          maxWidth: '1200px,
          margin: ''0 auto
        }}>''
          <h1 style={{
            margin: 0,
            color: '#1890ff',
            fontSize: '24px',
            fontWeight: 'bold'
          }}>
            📊 量化交易平台
          </h1>
          
          {/* 导航菜单 */}
          <nav style={{
            display: 'flex',
            gap: '24px'
          }}>
            <A
              href='/'
              style={{
                color: '#666',
                textDecoration: 'none',
                fontWeight: '500',
                padding: '8px 16px',
                borderRadius: '4px,
                transition: all 0.2s
              }}
              onMouseOver={(e) => {''''
                e.currentTarget.style.background = '#f0f9ff';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseOut={(e) => {''''
                e.currentTarget.style.background = transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              仪表盘''
            </A>'''
            <A ''''
              href='/strategy'''
              style={{ '''
                color: '#666,
                textDecoration: none,
                fontWeight: '500,
                padding: '8px 16px,
                borderRadius: '4px,
                transition: all 0.2s
              }}
              onMouseOver={(e) => {''''
                e.currentTarget.style.background = '#f0f9ff';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseOut={(e) => {''''
                e.currentTarget.style.background = transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              策略编辑''
            </A>'''
            <A ''''
              href='/backtest'''
              style={{ '''
                color: '#666,
                textDecoration: none,
                fontWeight: '500,
                padding: '8px 16px,
                borderRadius: '4px,
                transition: all 0.2s
              }}
              onMouseOver={(e) => {''''
                e.currentTarget.style.background = '#f0f9ff';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseOut={(e) => {''''
                e.currentTarget.style.background = transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              回测分析''
            </A>'''
            <A ''''
              href='/market'''
              style={{ '''
                color: '#666,
                textDecoration: none,
                fontWeight: '500,
                padding: '8px 16px,
                borderRadius: '4px,
                transition: all 0.2s
              }}
              onMouseOver={(e) => {''''
                e.currentTarget.style.background = '#f0f9ff';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseOut={(e) => {''''
                e.currentTarget.style.background = transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              市场数据''
            </A>'''
            <A ''''
              href='/test'''
              style={{ '''
                color: '#666,
                textDecoration: none,
                fontWeight: '500,
                padding: '8px 16px,
                borderRadius: '4px,
                transition: all 0.2s
              }}
              onMouseOver={(e) => {''''
                e.currentTarget.style.background = '#f0f9ff';
                e.currentTarget.style.color = '#1890ff';
              }}
              onMouseOut={(e) => {''''
                e.currentTarget.style.background = transparent';
                e.currentTarget.style.color = '#666';
              }}
            >
              测试页面
            </A>
          </nav>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main style={{'''
        flex: 1,
        padding: '24px,
        overflow: auto,
        maxWidth: '1200px,
        margin: '0 auto,
        width: ''100%
      }}>
        {props.children}
      </main>
    </div>''
  );
}