import { createSignal, createEffect, onCleanup, onMount, Show, For } from 'solid-js'
import { createStore } from 'solid-js/store'
import { css } from '../../styled-system/css'
import { formatCurrency, formatPercent } from '../../utils/formatters'
// Temporarily disabled - use<PERSON>tom is not compatible with SolidJS
// import { useAtom } from jotai
// import { tradingAtom, userAtom, marketDataAtom } from ../../stores/atoms
interface StockInfo {
  symbol: string
  name: string
  currentPrice: number
  change: number
  changePercent: number
  exchange?: string
}

interface Position {
  symbol: string
  totalQuantity: number
  availableQuantity: number
  avgPrice: number
  unrealizedPnl: number
  unrealizedPnlPercent: number
}

interface RiskWarning {
  type: string
  level: 'info' |'warning' |'error'
  message: string
}

interface OrderFormData {
  symbol: string'
  side: 'buy' |'sell''
  orderType: 'limit' |'market' |'stop' |'stop-profit
  quantity: number
  price: number
}

interface Props {
  defaultSymbol?: string'
  defaultSide?: 'buy' |'sell'
  quickTradeMode?: boolean
  onSubmit?: (data: OrderFormData) => void
  onStockSelect?: (stock: StockInfo) => void'
  onSideChange?: (side: 'buy' |'sell') => void
}

export function OrderForm(props: Props) {
  // State management - using SolidJS signals instead of Jotai atoms
  const [selectedStock, setSelectedStock] = createSignal<StockInfo | null>(null)
  const [orderSide, setOrderSide] = createSignal<buy |'sell'>(props.defaultSide ||'buy')
  const [orderType, setOrderType] = createSignal<'limit' |'market' |'stop' |'stop-profit'>('limit')
  const [quantity, setQuantity] = createSignal(0)
  const [price, setPrice] = createSignal(0)
  const [position, setPosition] = createSignal<Position | null>(null)
  const [riskWarnings, setRiskWarnings] = createSignal<RiskWarning[]>([])
  const [isSubmitting, setIsSubmitting] = createSignal(false)

  // Mock data for development
  const mockStock: StockInfo = {
    symbol: props.defaultSymbol ||AAPL,
    name: Apple Inc.',
    currentPrice: 150.00,
    change: 2.50,
    changePercent: 1.7,'
    exchange: 'NASDAQ'
  }

  onMount(() => {
    setSelectedStock(mockStock)
    setPrice(mockStock.currentPrice)
  })

  const handleSubmit = async () => {
    if (!selectedStock()) return
    
    setIsSubmitting(true)
    try {
      const formData: OrderFormData = {
        symbol: selectedStock()!.symbol,
        side: orderSide(),
        orderType: orderType(),
        quantity: quantity(),
        price: price()
      }
      
      props.onSubmit?.(formData)
      
      // Reset form
      setQuantity(0)
      if (orderType() !==market) {
        setPrice(selectedStock()!.currentPrice)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const calculateTotal = () => {
    return quantity() * price()
  }

  return (
    <div class={css({ 
      padding: '16px',
      backgroundColor: 'white',
      borderRadius: '8px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)
    })}>
      {/* Stock Info */}
      <Show when={selectedStock()}>
        <div class={css({ 
          marginBottom', 20px: '$4',
          padding: '12px',
          backgroundColor: '#f5f5f5',
          borderRadius: '6px'
        })}>
          <div class={css({ fontSize: '18px', fontWeight: 'bold' })}>
            {selectedStock()!.symbol} - {selectedStock()!.name}
          </div>
          <div class={css({ 
            marginTop: '8px',
            display: 'flex',
            gap: '16px'
          })}>
            <span class={css({ fontSize: '24px', fontWeight: 'bold' })}>
              {formatCurrency(selectedStock()!.currentPrice)}
            </span>
            <span class={css({ 
              color: selectedStock()!.change >= 0 ?#16a34a: '#dc2626',
              fontSize: '16px',
              alignSelf: 'center'
            })}>
              {selectedStock()!.change >= 0 ?+: ''}{selectedStock()!.change} 
              ({formatPercent(selectedStock()!.changePercent / 100)})
            </span>
          </div>
        </div>
      </Show>

      {/* Order Side */}
      <div class={css({ marginBottom: '20px' })}>
        <div class={css({ display: 'flex', gap: '8px' })}>
          <button
            class={css({
              flex: 1,'
              padding: '12px',
              fontSize: '16px',
              fontWeight: 'bold',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              backgroundColor: orderSide() ==='buy' ? '#dc2626: '#f5f5f5',
              color: orderSide() ==='buy' ?white: '#666'
            })}
            onClick={() => {'
              setOrderSide('buy')
              props.onSideChange?.('buy')
            }}
          >
            买入
          </button>
          <button
            class={css({
              flex: 1,'
              padding: '12px',
              fontSize: '16px',
              fontWeight: 'bold',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              backgroundColor: orderSide() ==='sell' ?#16a34a: '#f5f5f5',
              color: orderSide() ==='sell' ?white: '#666'
            })}
            onClick={() => {'
              setOrderSide('sell')
              props.onSideChange?.('sell')
            }}
          >
            卖出
          </button>
        </div>
      </div>

      {/* Order Type */}
      <div class={css({ marginBottom: '20px' })}>
        <label class={css({ display: 'block', marginBottom: '8px', fontWeight: '500' })}>
          订单类型
        </label>
        <select
          value={orderType()}
          onChange={(e) => setOrderType(e.target.value as any)}
          class={css({'
            width: ''100%,
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          })}
        >
          <option value="limit">限价单</option>
          <option value="market">市价单</option>
          <option value="stop">止损单</option>
          <option value="stop-profit'>止盈单</option>
        </select>
      </div>

      {/* Price Input */}
      <Show when={orderType() !=='market'}>
        <div class={css({ marginBottom: '20px' })}>
          <label class={css({ display: 'block', marginBottom: '8px', fontWeight: '500' })}>
            价格
          </label>
          <input
            type='number'
            value={price()}
            onInput={(e) => setPrice(parseFloat(e.target.value) || 0)}
            class={css({'
              width: ''100%,
              padding: '8px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '14px'
            })}
            step='0.01'
            min='0'
          />
        </div>
      </Show>

      {/* Quantity Input */}
      <div class={css({ marginBottom: '20px' })}>
        <label class={css({ display: 'block', marginBottom: '8px', fontWeight: '500' })}>
          数量
        </label>
        <input
          type='number'
          value={quantity()}
          onInput={(e) => setQuantity(parseInt(e.target.value) || 0)}
          class={css({'
            width: ''100%,
            padding: '8px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px'
          })}
          step='1'
          min='0'
        />
      </div>

      {/* Total Amount */}
      <div class={css({ 
        padding: '12px',
        backgroundColor: '#f5f5f5',
        borderRadius: '6px',
        marginBottom: '20px'
      })}>
        <div class={css({ display: 'flex', justifyContent: 'space-between' })}>
          <span>预计金额:</span>
          <span class={css({ fontWeight: 'bold', fontSize: '18px' })}>
            {formatCurrency(calculateTotal())}
          </span>
        </div>
      </div>

      {/* Risk Warnings */}
      <Show when={riskWarnings().length > 0}>
        <div class={css({ marginBottom: '20px' })}>
          <For each={riskWarnings()}>
            {(warning) => (
              <div class={css({ 
                padding: '8px',
                marginBottom: '8px',
                borderRadius: '4px',
                backgroundColor: warning.level ==='error' ?'#fee2e2' '
                               warning.level ==='warning' ? '#fef3c7: '#dbeafe',
                color: warning.level ==='error' ?'#dc2626' '
                       warning.level ==='warning' ? '#d97706: '#2563eb',
                fontSize: '14px'
              })}>
                {warning.message}
              </div>
            )}
          </For>
        </div>
      </Show>

      {/* Submit Button */}
      <button
        onClick={handleSubmit}
        disabled={isSubmitting() || quantity() === 0}
        class={css({'
          width: ''100%,
          padding: '14px',
          fontSize: '16px',
          fontWeight: 'bold',
          border: 'none',
          borderRadius: '6px',
          cursor: isSubmitting() || quantity() === 0 ?not-allowed: 'pointer',
          backgroundColor: orderSide() ==='buy' ? '#dc2626: '#16a34a',
          color: 'white',
          opacity: isSubmitting() || quantity() === 0 ? 0.5 : 1,'
          transition: 'opacity 0.2s'
        })}
      >
        {isSubmitting() ?'提交中...' : `确认${orderSide() ==='buy' ?买入: '卖出'}`}
      </button>
    </div>
  )
}
'
export default OrderForm